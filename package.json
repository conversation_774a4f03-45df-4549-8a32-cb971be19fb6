{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hello-pangea/dnd": "^17.0.0", "@types/uuid": "^10.0.0", "axios": "^1.7.7", "date-fns": "^3.6.0", "firebase": "^10.13.0", "firebase-admin": "^12.5.0", "lucide-react": "^0.460.0", "next": "14.2.6", "react": "^18", "react-dom": "^18", "react-icons": "^5.3.0", "react-select": "^5.8.0", "react-switch": "^7.0.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.6", "postcss": "^8.4.41", "tailwindcss": "^3.4.10", "typescript": "^5"}}