'use client';

import { useState, useEffect } from 'react';
import { db, storage } from '@/config/firebaseConfig';
import { collection, getDocs, setDoc, doc, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { Upload, Image as ImageIcon, AlertCircle, Loader2 } from 'lucide-react';
import Image from 'next/image';

interface Slider {
    id: string;
    title: string;
    imageUrl: string;
    altText: string;
    order: number;
}

const DEFAULT_SLIDERS = [
    {
        id: 'slider1',
        title: 'Ana Slider',
        imageUrl: '/images/default-slider-1.jpg',
        altText: 'Ana slider açıklaması',
        order: 1
    },
    {
        id: 'slider2',
        title: 'İkinci Slider',
        imageUrl: '/images/default-slider-2.jpg',
        altText: 'İkinci slider açıklaması',
        order: 2
    },
    {
        id: 'slider3',
        title: 'Ü<PERSON><PERSON><PERSON><PERSON>lider',
        imageUrl: '/images/default-slider-3.jpg',
        altText: 'Üçüncü slider açıklaması',
        order: 3
    }
];

export default function SliderManagement() {
    const [sliders, setSliders] = useState<Slider[]>(DEFAULT_SLIDERS);
    const [loading, setLoading] = useState<{[key: string]: boolean}>({});
    const [error, setError] = useState<string | null>(null);
    const [selectedFiles, setSelectedFiles] = useState<{[key: string]: File | null}>({});
    const [altTexts, setAltTexts] = useState<{[key: string]: string}>({});

    // Firestore'dan slider verilerini çek
    useEffect(() => {
        const initializeSliders = async () => {
            try {
                setError(null);
                const slidersRef = collection(db, 'sliders');
                const querySnapshot = await getDocs(slidersRef);

                // Eğer slider koleksiyonu boşsa varsayılan değerleri yükle
                if (querySnapshot.empty) {
                    for (const slider of DEFAULT_SLIDERS) {
                        await setDoc(doc(db, 'sliders', slider.id), slider);
                    }
                    setSliders(DEFAULT_SLIDERS);
                    const defaultAltTexts = DEFAULT_SLIDERS.reduce((acc, slider) => ({
                        ...acc,
                        [slider.id]: slider.altText
                    }), {});
                    setAltTexts(defaultAltTexts);
                    return;
                }

                // Varolan slider verilerini yükle
                const slidersData = querySnapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data()
                } as Slider)).sort((a, b) => a.order - b.order);

                setSliders(slidersData);
                const existingAltTexts = slidersData.reduce((acc, slider) => ({
                    ...acc,
                    [slider.id]: slider.altText
                }), {});
                setAltTexts(existingAltTexts);

            } catch (err) {
                console.error('Veri yükleme hatası:', err);
                setError('Slider verileri yüklenirken bir hata oluştu.');
            }
        };

        initializeSliders();
    }, []);

    const handleAltTextChange = (sliderId: string, value: string) => {
        setAltTexts(prev => ({
            ...prev,
            [sliderId]: value
        }));
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, sliderId: string) => {
        if (e.target.files && e.target.files[0]) {
            setSelectedFiles(prev => ({
                ...prev,
                [sliderId]: e.target.files![0]
            }));
        }
    };

    const handleUpdateSlider = async (sliderId: string) => {
        const selectedFile = selectedFiles[sliderId];
        if (!selectedFile) return;

        setLoading(prev => ({ ...prev, [sliderId]: true }));
        setError(null);

        try {
            // Storage'a resmi yükle
            const storageRef = ref(storage, `sliders/${sliderId}`);
            await uploadBytes(storageRef, selectedFile);
            const downloadURL = await getDownloadURL(storageRef);

            // Firestore'u güncelle
            const sliderDoc = doc(db, 'sliders', sliderId);
            await updateDoc(sliderDoc, {
                imageUrl: downloadURL,
                altText: altTexts[sliderId] || ''
            });

            // State'i güncelle
            setSliders(prev => prev.map(slider =>
                slider.id === sliderId
                    ? { ...slider, imageUrl: downloadURL, altText: altTexts[sliderId] || '' }
                    : slider
            ));

            // Seçili dosyayı temizle
            setSelectedFiles(prev => ({
                ...prev,
                [sliderId]: null
            }));

        } catch (err) {
            console.error('Güncelleme hatası:', err);
            setError('Slider güncellenirken bir hata oluştu.');
        } finally {
            setLoading(prev => ({ ...prev, [sliderId]: false }));
        }
    };

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="max-w-6xl mx-auto p-6 sm:p-8">
                {/* Header Section */}
                <div className="mb-8">
                    <div className="flex items-center gap-3 mb-2">
                        <ImageIcon className="w-8 h-8 text-indigo-600" />
                        <h1 className="text-2xl font-bold text-gray-800">Slider Yönetimi</h1>
                    </div>
                    <p className="text-gray-600">Ana sayfa sliderlarını buradan yönetebilirsiniz.</p>
                    
                    {error && (
                        <div className="mt-4 flex items-center gap-2 p-4 bg-red-50 rounded-lg text-red-700 border border-red-100">
                            <AlertCircle className="w-5 h-5" />
                            <p>{error}</p>
                        </div>
                    )}
                </div>

                {/* Sliders Grid */}
                <div className="space-y-6">
                    {sliders.map((slider) => (
                        <div
                            key={slider.id}
                            className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200"
                        >
                            <div className="p-6">
                                <h2 className="text-lg font-semibold text-gray-800 mb-6 flex items-center gap-2">
                                    <span className="w-8 h-8 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center">
                                        {slider.order}
                                    </span>
                                    {slider.title}
                                </h2>

                                <div className="grid lg:grid-cols-2 gap-8">
                                    {/* Image Preview */}
                                    <div className="space-y-3">
                                        <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden border-2 border-gray-200">
                                            <div className="relative w-full h-full">
                                                <Image
                                                    src={slider.imageUrl}
                                                    alt={slider.altText}
                                                    fill
                                                    className="rounded-lg object-cover"
                                                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                                />
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2 text-sm text-gray-500">
                                            <ImageIcon className="w-4 h-4" />
                                            <span>Önerilen boyut: 1900x800px</span>
                                        </div>
                                    </div>

                                    {/* Controls */}
                                    <div className="space-y-6">
                                        {/* Alt Text Input */}
                                        <div className="space-y-2">
                                            <label
                                                htmlFor={`alt-${slider.id}`}
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Görsel Açıklaması (Alt Text)
                                            </label>
                                            <input
                                                type="text"
                                                id={`alt-${slider.id}`}
                                                value={altTexts[slider.id] || ''}
                                                onChange={(e) => handleAltTextChange(slider.id, e.target.value)}
                                                className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                placeholder="Görsel açıklaması girin"
                                            />
                                        </div>

                                        {/* File Input */}
                                        <div className="space-y-2">
                                            <label
                                                htmlFor={`file-${slider.id}`}
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Yeni Görsel Seç
                                            </label>
                                            <div className="relative">
                                                <input
                                                    id={`file-${slider.id}`}
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={(e) => handleFileChange(e, slider.id)}
                                                    className="w-full px-4 py-2 rounded-lg border border-gray-300 
                                                             file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0
                                                             file:text-sm file:font-medium file:bg-indigo-50 
                                                             file:text-indigo-600 hover:file:bg-indigo-100
                                                             focus:outline-none"
                                                />
                                            </div>
                                        </div>

                                        {/* Update Button */}
                                        <button
                                            onClick={() => handleUpdateSlider(slider.id)}
                                            disabled={loading[slider.id] || !selectedFiles[slider.id]}
                                            className={`w-full px-4 py-3 rounded-lg font-medium flex items-center justify-center gap-2
                                                      transition-all duration-200 ${
                                                loading[slider.id] || !selectedFiles[slider.id]
                                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                    : 'bg-indigo-600 text-white hover:bg-indigo-700'
                                            }`}
                                        >
                                            {loading[slider.id] ? (
                                                <>
                                                    <Loader2 className="w-5 h-5 animate-spin" />
                                                    <span>Yükleniyor...</span>
                                                </>
                                            ) : (
                                                <>
                                                    <Upload className="w-5 h-5" />
                                                    <span>Görseli Güncelle</span>
                                                </>
                                            )}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}