'use client';

import { useState, useEffect } from 'react';
import { db } from '@/config/firebaseConfig';
import { collection, getDocs, getDoc, doc } from 'firebase/firestore';
import { 
    Building2, 
    ShoppingCart, 
    Users, 
    Layout, 
    Truck, 
    Package2, 
    TrendingUp, 
    ArrowUpRight, 
    ArrowDownRight 
} from 'lucide-react';
import Modal from '@/components/StaticsModal';

interface OrderDetail {
    id: string;
    orderDate: Date;
    status: string;
    totalPrice: number;
    totalPurchasePrice: number;
    items: Array<{
        name: string;
        price: number;
        purchasePrice: number;
        selectedFeatures: any[];
        selectedFeaturesPriceTotal: number;
        totalPurchasePrice: number;
        quantity: number;
        firmName: string;
        firmId: string;
        categoryName: string;
        productId: string;
    }>;
}

const calculateOrderTotal = (orderData: any): number => {
    // Önce status kontrolü
    if (orderData.status === 'İptal' || orderData.status === 'İade') {
        return 0;
    }

    // totalPrice varsa sadece onu kullan
    if (orderData.totalPrice) {
        const total = parseFloat(orderData.totalPrice) || 0;
        return total;
    }

    // totalPrice yoksa items içindeki price'ları topla
    let total = 0;
    if (orderData.items && Array.isArray(orderData.items)) {
        orderData.items.forEach((item: any) => {
            const itemPrice = parseFloat(item.price) || 0;
            const quantity = parseInt(item.quantity) || 1;
            total += itemPrice * quantity;
        });
    }

    // Hiç price bulunamadıysa
    if (total === 0) {
        console.warn(`No price found for order: ${orderData.id}`);
    }

    return total;
};

const getPercentageChange = (current: number, previous: number) => {
    if (previous === 0) return 100;
    return ((current - previous) / previous * 100).toFixed(1);
};

export default function Dashboard() {
    const [stats, setStats] = useState({
        firmsCount: 0,
        ordersCount: 0,
        usersCount: 0,
        categoriesCount: 0,
        couriersCount: 0,
        productsCount: 0,
        totalRevenue: 0,
        last24HoursRevenue: 0,
        currentDayRevenue: 0,
        last7DaysRevenue: 0,
        monthlyRevenue: 0,
        last30DaysRevenue: 0,
    });
    const [showRevenueDetails, setShowRevenueDetails] = useState(false);
    const [orderDetails, setOrderDetails] = useState<OrderDetail[]>([]);
    const [selectedTimeFrame, setSelectedTimeFrame] = useState<string>(''); // 'total', 'monthly', 'weekly', '24h', 'today'
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        const fetchCounts = async () => {
            try {
                const snapshots = await Promise.all([
                    getDocs(collection(db, 'firms')),
                    getDocs(collection(db, 'users')),
                    getDocs(collection(db, 'categories')),
                    getDocs(collection(db, 'couriers')),
                    getDocs(collection(db, 'products')),
                    getDocs(collection(db, 'orders'))
                ]);

                // Türkiye saatini al
                const now = new Date();
                const turkeyOffset = 3; // UTC+3
                const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
                const turkeyTime = new Date(utc + (3600000 * turkeyOffset));

                // Zaman dilimlerini hesapla
                const last24Hours = new Date(turkeyTime.getTime() - (24 * 60 * 60 * 1000));
                
                const startOfDay = new Date(turkeyTime);
                startOfDay.setHours(0, 0, 0, 0);
                
                const last7Days = new Date(turkeyTime.getTime() - (7 * 24 * 60 * 60 * 1000));
                
                // Ayın başlangıcını hesapla
                const startOfMonth = new Date(turkeyTime.getFullYear(), turkeyTime.getMonth(), 1);
                startOfMonth.setHours(0, 0, 0, 0);

                const last30Days = new Date(turkeyTime.getTime() - (30 * 24 * 60 * 60 * 1000));
                let last30DaysRevenue = 0;

                console.log('Şu anki zaman:', turkeyTime);
                console.log('Ay başlangıcı:', startOfMonth);

                let totalRevenue = 0;
                let last24HoursRevenue = 0;
                let currentDayRevenue = 0;
                let last7DaysRevenue = 0;
                let monthlyRevenue = 0;

                const [
                    firmsSnapshot,
                    usersSnapshot,
                    categoriesSnapshot,
                    couriersSnapshot,
                    productsSnapshot,
                    ordersSnapshot
                ] = snapshots;

                ordersSnapshot.docs.forEach(doc => {
                    const data = doc.data();
                    
                    if (data.status === 'İptal' || data.status === 'İade') {
                        return;
                    }

                    const orderTotal = calculateOrderTotal(data);
                    
                    // Sipariş tarihini Türkiye saatine çevir
                    const orderTimestamp = data.orderDate.seconds * 1000;
                    const orderDate = new Date(orderTimestamp);
                    const orderUtc = orderDate.getTime() + (orderDate.getTimezoneOffset() * 60000);
                    const orderTurkeyTime = new Date(orderUtc + (3600000 * turkeyOffset));

                    console.log('Sipariş tarihi:', orderTurkeyTime);

                    totalRevenue += orderTotal;

                    if (orderTurkeyTime >= last24Hours) {
                        last24HoursRevenue += orderTotal;
                    }

                    if (orderTurkeyTime >= startOfDay) {
                        currentDayRevenue += orderTotal;
                    }

                    if (orderTurkeyTime >= last7Days) {
                        last7DaysRevenue += orderTotal;
                    }

                    if (orderTurkeyTime >= startOfMonth) {
                        console.log('Aylık ciroya eklenen sipariş:', orderTotal);
                        monthlyRevenue += orderTotal;
                    }

                    if (orderTurkeyTime >= last30Days) {
                        last30DaysRevenue += orderTotal;
                    }
                });

                setStats({
                    firmsCount: firmsSnapshot.size,
                    ordersCount: ordersSnapshot.size,
                    usersCount: usersSnapshot.size,
                    categoriesCount: categoriesSnapshot.size,
                    couriersCount: couriersSnapshot.size,
                    productsCount: productsSnapshot.size,
                    totalRevenue,
                    last24HoursRevenue,
                    currentDayRevenue,
                    last7DaysRevenue,
                    monthlyRevenue,
                    last30DaysRevenue,
                });

            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchCounts();
        const interval = setInterval(fetchCounts, 5 * 60 * 1000);
        return () => clearInterval(interval);
    }, []);

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY',
            minimumFractionDigits: 2
        }).format(amount);
    };
    const statsCards = [
        { title: 'Firmalar', value: stats.firmsCount, icon: Building2, color: 'blue' },
        { title: 'Siparişler', value: stats.ordersCount, icon: ShoppingCart, color: 'green' },
        { title: 'Kullanıcılar', value: stats.usersCount, icon: Users, color: 'purple' },
        { title: 'Kategoriler', value: stats.categoriesCount, icon: Layout, color: 'yellow' },
        { title: 'Kuryeler', value: stats.couriersCount, icon: Truck, color: 'red' },
        { title: 'Ürünler', value: stats.productsCount, icon: Package2, color: 'indigo' },
    ];

    const getTimeFrameTitle = (timeFrame: string) => {
        switch(timeFrame) {
            case 'total': return 'Tüm Zamanlar Gelir Detayları';
            case 'monthly': return 'Bu Ay Gelir Detayları';
            case 'weekly': return 'Son 7 Gün Gelir Detayları';
            case 'today': return 'Bugünün Gelir Detayları';
            case '24h': return 'Son 24 Saat Gelir Detayları';
            case 'last30': return 'Son 30 Gün Gelir Detayları';
            default: return 'Gelir Detayları';
        }
    };

    const isOrderInTimeFrame = (orderDate: Date, timeFrame: string) => {
        const now = new Date();
        const turkeyTimeStr = now.toLocaleString('en-US', { timeZone: 'Europe/Istanbul' });
        const turkeyTime = new Date(turkeyTimeStr);
        
        const startOfDay = new Date(turkeyTime);
        startOfDay.setHours(0, 0, 0, 0);
        
        const last24Hours = new Date(turkeyTime.getTime() - (24 * 60 * 60 * 1000));
        const last7Days = new Date(turkeyTime.getTime() - (7 * 24 * 60 * 60 * 1000));
        const startOfMonth = new Date(turkeyTime.getFullYear(), turkeyTime.getMonth(), 1);
        const last30Days = new Date(turkeyTime.getTime() - (30 * 24 * 60 * 60 * 1000));

        switch(timeFrame) {
            case 'total':
                return true;
            case 'monthly':
                return orderDate >= startOfMonth;
            case 'weekly':
                return orderDate >= last7Days;
            case 'today':
                return orderDate >= startOfDay;
            case '24h':
                return orderDate >= last24Hours;
            case 'last30':
                return orderDate >= last30Days;
            default:
                return false;
        }
    };

    const revenueCards = [
        { 
            title: 'Son 24 Saat Ciro', 
            value: stats.last24HoursRevenue,
            timeFrame: '24h'
        },
        { 
            title: 'Bugünün Cirosu', 
            value: stats.currentDayRevenue,
            timeFrame: 'today'
        },
        { 
            title: 'Son 7 Gün Ciro', 
            value: stats.last7DaysRevenue,
            timeFrame: 'weekly'
        },
        { 
            title: 'Son 30 Gün Ciro',
            value: stats.last30DaysRevenue,
            timeFrame: 'last30'
        },
        { 
            title: 'Bu Ay Toplam Ciro', 
            value: stats.monthlyRevenue,
            timeFrame: 'monthly'
        },
        { 
            title: 'Toplam Ciro', 
            value: stats.totalRevenue,
            timeFrame: 'total'
        }
    ];

    const fetchOrderDetails = async () => {
        setIsLoading(true);
        try {
            const ordersSnapshot = await getDocs(collection(db, 'orders'));
            const details: OrderDetail[] = [];

            for (const orderDoc of ordersSnapshot.docs) {
                const data = orderDoc.data();
                const orderTotal = calculateOrderTotal(data);
                let totalPurchasePrice = 0;

                // Ürünlerin purchase price'larını ve selectedFeatures'larını almak için
                const itemsWithPurchasePrice = await Promise.all(data.items.map(async (item: any) => {
                    let purchasePrice = 0;
                    if (item.productId) {
                        const productDoc = await getDoc(doc(db, 'products', item.productId));
                        if (productDoc.exists()) {
                            purchasePrice = productDoc.data().purchasePrice || 0;
                        }
                    }

                    // selectedFeatures'ların fiyatlarını topla
                    const selectedFeaturesPriceTotal = item.selectedFeatures?.reduce((total: number, feature: any) => {
                        return total + (parseFloat(feature.price) || 0);
                    }, 0) || 0;

                    // Toplam alış fiyatı = Ürün alış fiyatı + selectedFeatures fiyatları
                    const totalItemPurchasePrice = (purchasePrice + selectedFeaturesPriceTotal) * (item.quantity || 1);
                    totalPurchasePrice += totalItemPurchasePrice;
                    
                    return {
                        name: item.productName || '',
                        price: item.price || 0,
                        purchasePrice: purchasePrice,
                        selectedFeatures: item.selectedFeatures || [], // Seçilen özellikleri ekle
                        selectedFeaturesPriceTotal,
                        totalPurchasePrice: totalItemPurchasePrice,
                        quantity: item.quantity || 1,
                        firmName: item.firmName || '',
                        firmId: item.firmId || '',
                        categoryName: item.categoryName || '',
                        productId: item.productId || ''
                    };
                }));

                details.push({
                    id: orderDoc.id,
                    orderDate: new Date(data.orderDate.seconds * 1000),
                    status: data.status,
                    totalPrice: data.status === 'İptal' || data.status === 'İade' 
                        ? data.totalPrice || orderTotal 
                        : orderTotal,
                    totalPurchasePrice,
                    items: itemsWithPurchasePrice
                });
            }

            details.sort((a, b) => b.orderDate.getTime() - a.orderDate.getTime());
            setOrderDetails(details);
        } catch (error) {
            console.error('Error fetching order details:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const calculateTotalRevenue = (orders: OrderDetail[]) => {
        const validOrders = orders.filter(order => 
            order.status !== 'İptal' && order.status !== 'İade'
        );
        
        const revenue = validOrders.reduce((sum, order) => sum + order.totalPrice, 0);
        const purchasePrice = validOrders.reduce((sum, order) => sum + order.totalPurchasePrice, 0);
        
        return {
            revenue,
            purchasePrice,
            profit: revenue - purchasePrice
        };
    };

    return (
        <div className="p-8 space-y-8 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
            {/* Header Section */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800">Dashboard</h1>
                <p className="text-gray-600">Şirket metriklerinize genel bakış</p>
            </div>

            {/* Stats Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
                {statsCards.map((card, index) => (
                    <div
                        key={index}
                        className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1"
                    >
                        <div className="flex justify-between items-start mb-4">
                            <div className={`p-2 rounded-lg bg-${card.color}-100`}>
                                <card.icon className={`w-5 h-5 text-${card.color}-600`} />
                            </div>
                        </div>
                        <h3 className="text-gray-600 text-sm font-medium mb-2">{card.title}</h3>
                        <p className="text-2xl font-bold text-gray-800">{card.value.toLocaleString()}</p>
                    </div>
                ))}
            </div>

            {/* Revenue Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
                {revenueCards.map((card, index) => {
                    const isPositiveGrowth = Math.random() > 0.5;
                    const growthPercent = (Math.random() * 20).toFixed(1);

                    return (
                        <div
                            key={index}
                            className="group bg-white rounded-xl p-6 shadow-sm hover:shadow-lg 
                                      transition-all duration-300 cursor-pointer transform hover:-translate-y-1 
                                      relative overflow-hidden border border-gray-100"
                            onClick={() => {
                                setSelectedTimeFrame(card.timeFrame);
                                setShowRevenueDetails(true);
                                fetchOrderDetails();
                            }}
                        >
                            {/* Hover efekti için arka plan overlay */}
                            <div className="absolute inset-0 bg-teal-50/0 group-hover:bg-teal-50/30 
                                          transition-all duration-300"></div>
                            
                            {/* "Detayları Gör" yazısı */}
                            <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 
                                          transition-all duration-300 text-xs text-teal-600 font-medium 
                                          flex items-center gap-1">
                                Detayları Gör
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                                          d="M9 5l7 7-7 7" />
                                </svg>
                            </div>

                            <div className="flex justify-between items-start mb-4">
                                <div className="p-2 rounded-lg bg-teal-50">
                                    <TrendingUp className="w-5 h-5 text-teal-600" />
                                </div>
                                <div className={`flex items-center ${
                                    isPositiveGrowth ? 'text-green-600' : 'text-red-600'
                                } text-sm font-medium`}>
                                    {isPositiveGrowth ? (
                                        <ArrowUpRight className="w-4 h-4 mr-1" />
                                    ) : (
                                        <ArrowDownRight className="w-4 h-4 mr-1" />
                                    )}
                                    {growthPercent}%
                                </div>
                            </div>
                            <h3 className="text-gray-600 text-sm font-medium mb-2">{card.title}</h3>
                            <p className="text-2xl font-bold text-gray-800">
                                {formatCurrency(card.value)}
                            </p>
                        </div>
                    );
                })}
            </div>

            {/* Modal with Loading State */}
            {selectedTimeFrame && showRevenueDetails && (
                <Modal 
                    onClose={() => {
                        setShowRevenueDetails(false);
                        setSelectedTimeFrame('');
                    }} 
                    title={getTimeFrameTitle(selectedTimeFrame)}
                >
                    {isLoading ? (
                        <div className="flex flex-col items-center justify-center py-12">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-500 mb-4"></div>
                            <p className="text-gray-600 text-lg">Veriler yükleniyor...</p>
                             <p className="text-gray-600 text-lg">Hesaplamalar yapılıyor...</p>
                            <p className="text-gray-500 text-sm mt-2">Lütfen bekleyin</p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto rounded-lg border border-gray-200">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        {['Tarih', 'Sipariş ID', 'Ürünler', 'Durum', 'Toplam'].map((header) => (
                                            <th 
                                                key={header}
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                {header}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {orderDetails
                                        .filter(order => isOrderInTimeFrame(order.orderDate, selectedTimeFrame))
                                        .map((order) => (
                                            <tr 
                                                key={order.id} 
                                                className={`border-t ${
                                                    order.status === 'İptal' || order.status === 'İade' 
                                                    ? 'bg-red-50' 
                                                    : ''
                                                }`}
                                            >
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {order.orderDate.toLocaleDateString('tr-TR')}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {order.id}
                                                </td>
                                                <td className="px-6 py-4 text-sm text-gray-900">
                                                    {order.items.map((item, index) => (
                                                        <div key={index} className="mb-1">
                                                            <strong>{item.firmName}</strong>: {item.name} ({item.categoryName}) 
                                                            x {item.quantity} = {formatCurrency(item.price * item.quantity)}
                                                            <div className="text-xs text-gray-600 ml-4">
                                                                <div>Ürün Alış: {formatCurrency(item.purchasePrice)}</div>
                                                                {item.selectedFeatures?.map((feature, idx) => (
                                                                    <div key={idx} className="text-gray-500">
                                                                        - {feature.name}: {formatCurrency(parseFloat(feature.price))}
                                                                    </div>
                                                                ))}
                                                                <div className="font-medium text-red-600">
                                                                    Toplam Alış: {formatCurrency(item.totalPurchasePrice)}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </td>
                                                <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                                                    order.status === 'İptal' || order.status === 'İade'
                                                    ? 'text-red-600 font-semibold'
                                                    : ''
                                                }`}>
                                                    {order.status}
                                                </td>
                                                <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                                                    order.status === 'İptal' || order.status === 'İade'
                                                    ? 'text-red-600 font-semibold'
                                                    : ''
                                                }`}>
                                                    {formatCurrency(order.totalPrice)}
                                                    <div className="text-red-600 text-xs">
                                                        Alış: {formatCurrency(order.totalPurchasePrice)}
                                                    </div>
                                                    {(order.status === 'İptal' || order.status === 'İade') && (
                                                        <span className="text-red-600 text-xs block">
                                                            (Hesaplamaya dahil edilmedi)
                                                        </span>
                                                    )}
                                                </td>
                                            </tr>
                                        ))}
                                </tbody>
                                <tfoot>
                                    <tr className="bg-gray-50">
                                        <td colSpan={4} className="px-6 py-4 text-sm font-medium text-gray-900 text-right">
                                            {getTimeFrameTitle(selectedTimeFrame)} Toplam:
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                                            {formatCurrency(
                                                calculateTotalRevenue(
                                                    orderDetails.filter(order => 
                                                        isOrderInTimeFrame(order.orderDate, selectedTimeFrame)
                                                    )
                                                ).revenue
                                            )}
                                            <div className="text-red-600 text-xs font-normal">
                                                Toplam Alış: {formatCurrency(
                                                    calculateTotalRevenue(
                                                        orderDetails.filter(order => 
                                                            isOrderInTimeFrame(order.orderDate, selectedTimeFrame)
                                                        )
                                                    ).purchasePrice
                                                )}
                                            </div>
                                            <div className="text-green-600 text-xs font-normal">
                                                Kar: {formatCurrency(
                                                    calculateTotalRevenue(
                                                        orderDetails.filter(order => 
                                                            isOrderInTimeFrame(order.orderDate, selectedTimeFrame)
                                                        )
                                                    ).profit
                                                )}
                                            </div>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    )}
                </Modal>
            )}
        </div>
    );
}