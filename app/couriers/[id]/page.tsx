'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { db } from '@/config/firebaseConfig';
import { doc, getDoc, updateDoc } from 'firebase/firestore';

export default function UpdateCourierPage() {
    const [name, setName] = useState('');
    const [courierId, setCourierId] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [phone, setPhone] = useState('');
    const router = useRouter();
    const { id } = useParams(); // Kurye ID'sini URL'den almak için kullanılır

    useEffect(() => {
        const fetchCourier = async () => {
            if (typeof id === 'string') {
                const courierDoc = await getDoc(doc(db, 'couriers', id));
                if (courierDoc.exists()) {
                    const courierData = courierDoc.data();
                    setName(courierData.name || '');
                    setCourierId(courierData.courierId || '');
                    setEmail(courierData.email || '');
                    setPassword(courierData.password || '');
                    setPhone(courierData.phone || '');
                } else {
                    console.error('No such document!');
                }
            }
        };

        fetchCourier();
    }, [id]);

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        try {
            if (typeof id === 'string') {
                await updateDoc(doc(db, 'couriers', id), {
                    name,
                    courierId,
                    email,
                    password,
                    phone,
                });

                router.push('/couriers');
            }
        } catch (error) {
            console.error('Error updating document: ', error);
        }
    };

    return (
        <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">Kurye Güncelle</h1>
            <form onSubmit={handleSubmit} className="space-y-4">
                <input
                    type="text"
                    placeholder="Kurye İsim Soyisim"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="text"
                    placeholder="Kurye ID"
                    value={courierId}
                    onChange={(e) => setCourierId(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="email"
                    placeholder="Kurye Email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="password"
                    placeholder="Kurye Şifre"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="text"
                    placeholder="Telefon Numarası"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded-md">Güncelle</button>
            </form>
        </div>
    );
}