'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { db } from '@/config/firebaseConfig';
import { collection, getDocs, deleteDoc, doc } from 'firebase/firestore';
import Link from 'next/link';

interface Courier {
    id: string;
    name: string;
    courierId: string;
    email: string;
    phone: string;
}

export default function CouriersPage() {
    const [couriers, setCouriers] = useState<Courier[]>([]);
    const router = useRouter();

    useEffect(() => {
        const fetchCouriers = async () => {
            const querySnapshot = await getDocs(collection(db, 'couriers'));
            const couriersList = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Courier));
            setCouriers(couriersList);
        };

        fetchCouriers();
    }, []);

    const handleDelete = async (id: string) => {
        await deleteDoc(doc(db, 'couriers', id));
        setCouriers(couriers.filter(courier => courier.id !== id));
    };

    return (
        <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">Kuryeler</h1>
            <Link href="/couriers/new">
                <button className="bg-orange-500 text-white px-4 py-2 rounded-md mb-4">Yeni Kurye Ekle</button>
            </Link>
            <ul className="space-y-4">
                {couriers.map((courier) => (
                    <li key={courier.id} className="flex justify-between items-center bg-white p-4 rounded-lg shadow">
                        <div>
                            <h2 className="text-xl font-bold">{courier.name}</h2>
                            <p>ID: {courier.courierId}</p>
                            <p>Email: {courier.email}</p>
                            <p>Telefon: {courier.phone}</p>
                        </div>
                        <div>
                            <Link href={`/couriers/${courier.id}`}>
                                <button className="bg-blue-500 text-white px-4 py-2 rounded-md mr-2">Güncelle</button>
                            </Link>
                            <button onClick={() => handleDelete(courier.id)} className="bg-red-500 text-white px-4 py-2 rounded-md">
                                Sil
                            </button>
                        </div>
                    </li>
                ))}
            </ul>
        </div>
    );
}
