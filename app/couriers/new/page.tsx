'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { db } from '@/config/firebaseConfig'; // Firebase Firestore yapılandırması
import { collection, addDoc } from 'firebase/firestore';

export default function NewCourierPage() {
    const [name, setName] = useState('');
    const [courierId, setCourierId] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [phone, setPhone] = useState('');
    const router = useRouter();

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        try {
            await addDoc(collection(db, 'couriers'), {
                name,
                courierId,
                email,
                password,
                phone,
            });

            router.push('/couriers');
        } catch (error) {
            console.error("Error adding document: ", error);
        }
    };

    return (
        <div className="p-6">
            <h1 className="text-2xl font-bold mb-4"><PERSON><PERSON><PERSON></h1>
            <form onSubmit={handleSubmit} className="space-y-4">
                <input
                    type="text"
                    placeholder="Kurye İsim Soyisim"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="text"
                    placeholder="Kurye ID"
                    value={courierId}
                    onChange={(e) => setCourierId(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="email"
                    placeholder="Kurye Email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="password"
                    placeholder="Kurye Şifre"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="text"
                    placeholder="Telefon Numarası"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <button type="submit" className="bg-green-500 text-white px-4 py-2 rounded-md">Ekle</button>
            </form>
        </div>
    );
}
