
'use client';
import React from 'react';
import { Star, Loader2 } from 'lucide-react';
import { db } from '@/config/firebaseConfig';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';

interface Review {
    rating: number;
    comment: string;
}

interface OrderItem {
    id: string;
    firmId: string;
    productName: string;
    review?: Review;
}

interface Order {
    id: string;
    items: OrderItem[];
    firstName: string;
    lastName: string;
}

interface Firm {
    id: string;
    name: string;
}

interface ReviewWithDetails extends Review {
    firstName: string;
    lastName: string;
    productName: string;
    firmName: string;
}

export default function AllReviews() {
    const [reviews, setReviews] = React.useState<ReviewWithDetails[]>([]);
    const [firms, setFirms] = React.useState<Firm[]>([]);
    const [selectedFirm, setSelectedFirm] = React.useState<string>('');
    const [isLoading, setIsLoading] = React.useState(true);

    React.useEffect(() => {
        const fetchFirms = async () => {
            const firmsSnapshot = await getDocs(collection(db, 'firms'));
            const firmsData: Firm[] = firmsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Firm[];
            setFirms(firmsData);
        };

        fetchFirms();
    }, []);

    React.useEffect(() => {
        const fetchReviews = async () => {
            try {
                const ordersSnapshot = await getDocs(collection(db, 'orders'));
                const fetchedReviews: ReviewWithDetails[] = [];

                for (const orderDoc of ordersSnapshot.docs) {
                    const orderData = orderDoc.data() as Order;

                    for (const item of orderData.items) {
                        if (item.review) {
                            const firmDoc = await getDoc(doc(db, 'firms', item.firmId));
                            const firmName = firmDoc.exists() ? firmDoc.data().name : 'Firma adı bulunamadı';

                            fetchedReviews.push({
                                ...item.review,
                                firstName: orderData.firstName,
                                lastName: orderData.lastName,
                                productName: item.productName,
                                firmName: firmName
                            });
                        }
                    }
                }

                setReviews(fetchedReviews);
            } catch (error) {
                console.error('Error fetching reviews:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchReviews();
    }, []);

    const renderStars = (rating: number) => (
        <div className="flex space-x-0.5">
            {[1, 2, 3, 4, 5].map((star) => (
                <Star
                    key={star}
                    className={`w-4 h-4 ${
                        star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-200'
                    } transition-colors duration-200`}
                />
            ))}
        </div>
    );

    const filteredReviews = selectedFirm
        ? reviews.filter(review => review.firmName === selectedFirm)
        : reviews;

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <Loader2 className="h-8 w-8 animate-spin text-orange-600" />
            </div>
        );
    }

    return (
        <div className="bg-white rounded-xl shadow-lg p-6 w-full">
            <div className="border-b pb-4 mb-6">
                <h2 className="text-2xl font-semibold text-gray-900">Müşteri Değerlendirmeleri</h2>
            </div>

            <div className="mb-6">
                <label
                    htmlFor="firmFilter"
                    className="block text-sm font-medium text-gray-700 mb-2"
                >
                    Firma Seçin
                </label>
                <select
                    id="firmFilter"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
                    value={selectedFirm}
                    onChange={(e) => setSelectedFirm(e.target.value)}
                >
                    <option value="">Tüm Firmalar</option>
                    {firms.map((firm) => (
                        <option key={firm.id} value={firm.name}>
                            {firm.name}
                        </option>
                    ))}
                </select>
            </div>

            {filteredReviews.length === 0 ? (
                <div className="bg-orange-50 border border-orange-200 text-orange-700 px-4 py-3 rounded-lg">
                    Bu kriterlere uygun değerlendirme bulunmamaktadır.
                </div>
            ) : (
                <div className="space-y-4">
                    {filteredReviews.map((review, index) => (
                        <div
                            key={index}
                            className="p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-all duration-200 border border-gray-100"
                        >
                            <div className="flex flex-col space-y-3">
                                <div className="flex items-center justify-between">
                                    {renderStars(review.rating)}
                                    <span className="text-sm font-medium text-gray-600">
                                        {review.firstName} {review.lastName}
                                    </span>
                                </div>
                                <div className="space-y-2">
                                    <h4 className="font-medium text-gray-900">
                                        {review.productName}
                                    </h4>
                                    <p className="text-gray-700 text-sm leading-relaxed">
                                        {review.comment}
                                    </p>
                                    <span className="inline-block text-xs font-medium text-orange-600 bg-orange-50 px-2 py-1 rounded-full">
                                        {review.firmName}
                                    </span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}