'use client';

import { useState, useMemo, useEffect, Suspense } from 'react';
import dynamic from 'next/dynamic';
import { deleteDoc, doc, updateDoc } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';
import Link from 'next/link';
import Button from '@/components/Button';
import DeleteModal from '@/components/DeleteModal';
import useFirestoreCollection from '@/hooks/useFirestoreCollection';
import { Box as Package2, Plus, Search, Grid, Pencil, Trash2, X } from 'lucide-react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { DragDropContext as DragDropContextComponent, Droppable as DroppableComponent, Draggable as DraggableComponent, DropResult } from '@hello-pangea/dnd';

interface Product {
    id: string;
    name: string;
    description: string;
    price: number;
    firmId: string;
    category: string;
    images: string[];
    order: number;
}

interface Firm {
    id: string;
    name: string;
}

interface Category {
    id: string;
    name: string;
}

const ProductsContent = () => {
    const router = useRouter();
    const searchParams = useSearchParams();

    const { data: products, loading: productsLoading, setData: setProducts } = useFirestoreCollection<Product>('products');
    const { data: firms, loading: firmsLoading } = useFirestoreCollection<Firm>('firms');
    const { data: categories, loading: categoriesLoading } = useFirestoreCollection<Category>('categories');

    const [selectedCategory, setSelectedCategory] = useState<string>(() => {
        const urlCategory = searchParams.get('category');
        return urlCategory || '';
    });

    const [selectedFirm, setSelectedFirm] = useState<string>(() => {
        const urlFirm = searchParams.get('firm');
        return urlFirm || '';
    });

    const [searchTerm, setSearchTerm] = useState<string>(() => {
        const urlSearch = searchParams.get('search');
        return urlSearch || '';
    });

    const [showModal, setShowModal] = useState<boolean>(false);
    const [productToDelete, setProductToDelete] = useState<string | null>(null);
    const [showAlert, setShowAlert] = useState(false);

    const handleClearFilters = () => {
        setSelectedCategory('');
        setSelectedFirm('');
        setSearchTerm('');

        if (typeof window !== 'undefined') {
            localStorage.removeItem('productFilters.category');
            localStorage.removeItem('productFilters.firm');
            localStorage.removeItem('productFilters.search');
        }

        window.history.replaceState({}, '', window.location.pathname);
    };

    useEffect(() => {
        const savedCategory = localStorage.getItem('productFilters.category');
        const savedFirm = localStorage.getItem('productFilters.firm');
        const savedSearch = localStorage.getItem('productFilters.search');

        if (!searchParams.get('category') && savedCategory) setSelectedCategory(savedCategory);
        if (!searchParams.get('firm') && savedFirm) setSelectedFirm(savedFirm);
        if (!searchParams.get('search') && savedSearch) setSearchTerm(savedSearch);
    }, [searchParams]);

    useEffect(() => {
        const params = new URLSearchParams();
        if (selectedCategory) params.set('category', selectedCategory);
        if (selectedFirm) params.set('firm', selectedFirm);
        if (searchTerm) params.set('search', searchTerm);

        const url = params.toString() ? `?${params.toString()}` : '';
        window.history.replaceState({}, '', url);

        if (typeof window !== 'undefined') {
            localStorage.setItem('productFilters.category', selectedCategory);
            localStorage.setItem('productFilters.firm', selectedFirm);
            localStorage.setItem('productFilters.search', searchTerm);
        }
    }, [selectedCategory, selectedFirm, searchTerm]);

    const handleCategoryChange = (value: string) => {
        setSelectedCategory(value);
    };

    const handleFirmChange = (value: string) => {
        setSelectedFirm(value);
    };

    const handleSearchChange = (value: string) => {
        setSearchTerm(value);
    };

    const confirmDelete = (id: string) => {
        setProductToDelete(id);
        setShowModal(true);
    };

    const handleDelete = async () => {
        if (productToDelete) {
            await deleteDoc(doc(db, 'products', productToDelete));
            setProducts((prevProducts: Product[]) => prevProducts.filter((product: Product) => product.id !== productToDelete));
            setShowModal(false);
            setProductToDelete(null);
        }
    };

    const getFirmName = (firmId: string) => {
        const firm = firms.find(f => f.id === firmId);
        return firm ? firm.name : 'Bilinmeyen Firma';
    };

    const getCategoryName = (categoryId: string) => {
        const category = categories.find(c => c.id === categoryId);
        return category ? category.name : 'Bilinmeyen Kategori';
    };

    const filteredProducts = useMemo(() => {
        return products
            .filter(product => {
                const normalizedSearchTerm = searchTerm
                    .normalize("NFD")
                    .replace(/[\u0300-\u036f]/g, "");

                const normalizedProductName = product.name
                    .normalize("NFD")
                    .replace(/[\u0300-\u036f]/g, "");

                return (
                    (selectedCategory ? product.category === selectedCategory : true) &&
                    (selectedFirm ? product.firmId === selectedFirm : true) &&
                    (searchTerm ? normalizedProductName.toUpperCase().includes(normalizedSearchTerm.toUpperCase()) : true)
                );
            })
            .sort((a, b) => (a.order || 0) - (b.order || 0));
    }, [products, selectedCategory, selectedFirm, searchTerm]);

    const handleDragEnd = async (result: DropResult) => {
        if (!result.destination) return;

        if (!selectedFirm) {
            setShowAlert(true);
            setTimeout(() => setShowAlert(false), 3000);
            return;
        }

        const items = Array.from(filteredProducts);
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);

        // Yeni sıraları güncelle - 10 yerine 1 kullanıyoruz
        const updates = items.map((item, index) => {
            return updateDoc(doc(db, 'products', item.id), {
                order: index + 1 // 10 yerine 1'er artacak şekilde değiştirdik
            });
        });

        try {
            await Promise.all(updates);

            // Local state'i güncelle
            setProducts(prevProducts => {
                const newProducts = [...prevProducts];
                const productMap = new Map(newProducts.map(p => [p.id, p]));

                items.forEach((item, index) => {
                    if (productMap.has(item.id)) {
                        const product = productMap.get(item.id)!;
                        product.order = index + 1; // Burayı da 1'er artacak şekilde güncelledik
                    }
                });

                return newProducts.sort((a, b) => (a.order || 0) - (b.order || 0));
            });
        } catch (error) {
            console.error('Sıralama güncellenirken hata oluştu:', error);
        }
    };

    if (productsLoading || firmsLoading || categoriesLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
            </div>
        );
    }


    return (
        <div className="min-h-screen bg-gray-50 p-6 sm:p-8">
            <div className="max-w-7xl mx-auto">
                {/* Header Section */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
                    <div className="flex items-center gap-3">
                        <Package2 className="w-8 h-8 text-orange-600" />
                        <h1 className="text-2xl font-bold text-gray-800">Ürünler</h1>
                    </div>

                    <div className="flex flex-wrap gap-3">
                        <Link href="/products/categories">
                            <Button className="bg-white text-gray-700 border border-gray-200 hover:bg-gray-50 flex items-center gap-2 px-4 py-2 shadow-sm">
                                <Grid className="w-4 h-4" />
                                <span>Kategoriler</span>
                            </Button>
                        </Link>
                        <Link href="/products/new">
                            <Button className="bg-orange-600 hover:bg-orange-700 text-white flex items-center gap-2 px-4 py-2 shadow-sm">
                                <Plus className="w-4 h-4" />
                                <span>Yeni Ürün</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Hata Uyarısı */}
                {showAlert && (
                    <div className="mb-6">
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <h3 className="text-sm font-medium text-red-800">
                                        Sıralama yapabilmek için önce bir firma seçmelisiniz!
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Bilgi Uyarısı */}
                <div className="mb-6 space-y-2">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-blue-800">Sıralama İşlemi Hakkında</h3>
                                <div className="mt-2 text-sm text-blue-700">
                                    <ul className="list-disc pl-5 space-y-1">
                                        <li>Önce firma filtreleme yapın, ardından sıralama işlemini gerçekleştirin. Filtreleme yapmadan sıralama yapmayın.</li>
                                        <li>Sürükleme işlemi sırasında lütfen ürünün yeni konumuna yerleşmesini bekleyin.</li>
                                        <li>Sıralama işlemi tüm ürünlerin sırasını günceller, bu işlem birkaç saniye sürebilir.</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters Section */}
                <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-1 relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                            <input
                                type="text"
                                placeholder="Ürün Ara..."
                                value={searchTerm}
                                onChange={(e) => handleSearchChange(e.target.value)}
                                className="pl-10 w-full p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                            />
                        </div>
                        <div className="flex gap-4">
                            <select
                                value={selectedCategory}
                                onChange={(e) => handleCategoryChange(e.target.value)}
                                className="min-w-[200px] p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                            >
                                <option value="">Tüm Kategoriler</option>
                                {categories.map(category => (
                                    <option key={category.id} value={category.id}>{category.name}</option>
                                ))}
                            </select>
                            <select
                                value={selectedFirm}
                                onChange={(e) => handleFirmChange(e.target.value)}
                                className="min-w-[200px] p-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                            >
                                <option value="" disabled>Firma Seçin</option>
                                {firms.map(firm => (
                                    <option key={firm.id} value={firm.id}>{firm.name}</option>
                                ))}
                            </select>
                            {(selectedCategory || selectedFirm || searchTerm) && (
                                <Button
                                    onClick={handleClearFilters}
                                    className="px-4 py-2 bg-gray-100 text-gray-600 hover:bg-gray-200 rounded-lg flex items-center gap-2"
                                >
                                    <X className="w-4 h-4" />
                                    <span>Filtreleri Temizle</span>
                                </Button>
                            )}
                        </div>
                    </div>
                </div>

                {/* Products Table */}
                <DragDropContextComponent onDragEnd={handleDragEnd}>
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead>
                                    <tr className="bg-gray-50">
                                        <th className="w-64 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ürün</th>
                                        <th className="w-96 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Açıklama</th>
                                        <th className="w-24 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fiyat</th>
                                        <th className="w-48 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Firma</th>
                                        <th className="w-40 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kategori</th>
                                        <th className="w-24 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">İşlemler</th>
                                    </tr>
                                </thead>
                                <DroppableComponent droppableId="products">
                                    {(provided) => (
                                        <tbody
                                            {...provided.droppableProps}
                                            ref={provided.innerRef}
                                            className="bg-white divide-y divide-gray-200"
                                        >
                                            {filteredProducts.length === 0 ? (
                                                <tr>
                                                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                                                        {searchTerm || selectedCategory || selectedFirm ?
                                                            'Arama kriterlerine uygun ürün bulunamadı.' :
                                                            'Henüz ürün bulunmuyor.'}
                                                    </td>
                                                </tr>
                                            ) : (
                                                filteredProducts.map((product, index) => (
                                                    <DraggableComponent
                                                        key={product.id}
                                                        draggableId={product.id}
                                                        index={index}
                                                    >
                                                        {(provided, snapshot) => (
                                                            <tr
                                                                ref={provided.innerRef}
                                                                {...provided.draggableProps}
                                                                {...provided.dragHandleProps}
                                                                className={`hover:bg-gray-50 cursor-move ${snapshot.isDragging ? 'bg-orange-50' : ''
                                                                    }`}
                                                            >
                                                                <td className="px-6 py-4 whitespace-nowrap">
                                                                    <div className="flex items-center">
                                                                        {product.images.length > 0 ? (
                                                                            <div className="relative w-12 h-12">
                                                                                <Image
                                                                                    src={product.images[0]}
                                                                                    alt={product.name}
                                                                                    fill
                                                                                    className="rounded-lg object-cover"
                                                                                    sizes="48px"
                                                                                />
                                                                            </div>
                                                                        ) : (
                                                                            <div className="w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center">
                                                                                <Package2 className="w-6 h-6 text-gray-400" />
                                                                            </div>
                                                                        )}
                                                                        <div className="ml-4 flex items-center gap-2">
                                                                            <div className="text-sm font-medium text-gray-900">{product.name}</div>
                                                                            {product.order !== undefined && product.order !== null && (
                                                                                <div className="bg-green-100 rounded-full p-0.5">
                                                                                    <svg 
                                                                                        xmlns="http://www.w3.org/2000/svg" 
                                                                                        className="h-5 w-5 text-green-600" 
                                                                                        viewBox="0 0 20 20" 
                                                                                        fill="currentColor"
                                                                                        stroke="white" 
                                                                                        strokeWidth="1"
                                                                                    >
                                                                                        <path 
                                                                                            fillRule="evenodd" 
                                                                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                                                                                            clipRule="evenodd" 
                                                                                        />
                                                                                    </svg>
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td className="px-6 py-4">
                                                                    <div className="text-sm text-gray-500 max-w-xs">
                                                                        <div className="truncate hover:whitespace-normal hover:text-clip" title={product.description}>
                                                                            {product.description}
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap">
                                                                    <div className="text-sm font-medium text-gray-900">{product.price.toLocaleString('tr-TR')} ₺</div>
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap">
                                                                    <div className="text-sm text-gray-900 truncate max-w-[200px]" title={getFirmName(product.firmId)}>
                                                                        {getFirmName(product.firmId)}
                                                                    </div>
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap">
                                                                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800" title={getCategoryName(product.category)}>
                                                                        {getCategoryName(product.category)}
                                                                    </span>
                                                                </td>
                                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                                    <div className="flex justify-end gap-2">
                                                                        <Link href={`/products/${product.id}`}>
                                                                            <Button className="p-2 text-blue-600 hover:text-blue-700">
                                                                                <Pencil className="w-4 h-4" />
                                                                            </Button>
                                                                        </Link>
                                                                        <Button
                                                                            onClick={() => confirmDelete(product.id)}
                                                                            className="p-2 text-red-600 hover:text-red-700"
                                                                        >
                                                                            <Trash2 className="w-4 h-4" />
                                                                        </Button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        )}
                                                    </DraggableComponent>
                                                ))
                                            )}
                                            {provided.placeholder}
                                        </tbody>
                                    )}
                                </DroppableComponent>
                            </table>
                        </div>
                    </div>
                </DragDropContextComponent>

                {showModal && <DeleteModal onClose={() => setShowModal(false)} onDelete={handleDelete} />}
            </div>
        </div>
    );
};

// Client-side only component
const ClientProductsContent = dynamic(() => Promise.resolve(ProductsContent), {
    ssr: false
});

const ProductsPage = () => {
    return (
        <Suspense fallback={
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
            </div>
        }>
            <ClientProductsContent />
        </Suspense>
    );
};

export default ProductsPage;