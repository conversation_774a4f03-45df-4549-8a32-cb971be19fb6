'use client';

import { useRouter } from 'next/navigation';
import { deleteDoc, doc } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';
import Link from 'next/link';
import { useFirestoreCollection } from '@/hooks/useFirestoreCollection';
import Button from '@/components/Button';
import DeleteModal from '@/components/DeleteModal';
import { useState, useMemo } from 'react';

interface Category {
    id: string;
    name: string;
}

export default function CategoriesPage() {
    const { data: categories, loading, setData: setCategories } = useFirestoreCollection<Category>('categories');
    const [showModal, setShowModal] = useState(false);
    const [categoryToDelete, setCategoryToDelete] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [categoriesPerPage] = useState<number>(10);
    const router = useRouter();

    const confirmDelete = (id: string) => {
        setCategoryToDelete(id);
        setShowModal(true);
    };

    const handleDelete = async () => {
        if (categoryToDelete) {
            await deleteDoc(doc(db, 'categories', categoryToDelete));
            setCategories(categories.filter(category => category.id !== categoryToDelete));
            setShowModal(false);
            setCategoryToDelete(null);
        }
    };

    const startEditing = (categoryId: string) => {
        router.push(`/products/categories/${categoryId}`);
    };

    const indexOfLastCategory = currentPage * categoriesPerPage;
    const indexOfFirstCategory = indexOfLastCategory - categoriesPerPage;
    const currentCategories = categories.slice(indexOfFirstCategory, indexOfLastCategory);
    const totalPages = Math.ceil(categories.length / categoriesPerPage);

    const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

    if (loading) return <div>Loading...</div>;

    return (
        <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">Kategoriler</h1>
            <Link href="/products/categories/new">
                <Button className="bg-orange-500 text-white px-4 py-2 rounded-md mb-4">Yeni Kategori Ekle</Button>
            </Link>
            <div className="overflow-x-auto">
                <table className="min-w-full bg-white border-collapse">
                    <thead>
                        <tr>
                            <th className="py-2 px-4 border border-gray-300">Kategori Adı</th>
                            <th className="py-2 px-4 border border-gray-300">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        {currentCategories.map((category) => (
                            <tr key={category.id} className="hover:bg-gray-100">
                                <td className="py-2 px-4 border border-gray-300">{category.name}</td>
                                <td className="py-2 px-4 border border-gray-300">
                                    <div className="flex items-center">
                                        <Button onClick={() => startEditing(category.id)} className="bg-blue-500 text-white px-4 py-2 rounded-md mr-2">Güncelle</Button>
                                        <Button onClick={() => confirmDelete(category.id)} className="bg-red-500 text-white px-4 py-2 rounded-md">Sil</Button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            {showModal && <DeleteModal onClose={() => setShowModal(false)} onDelete={handleDelete} />}
            <div className="flex justify-center mt-4 md:mt-12">
                <nav>
                    <ul className="flex">
                        <li className="mx-1 md:mx-3">
                            <Button onClick={() => paginate(1)} className="bg-white shadow-lg hover:bg-gray-100">İlk</Button>
                        </li>
                        <li className="mx-1 md:mx-3">
                            <Button onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)} className="bg-white shadow-lg hover:bg-gray-100">Önceki</Button>
                        </li>
                        <li className="mx-1 md:mx-3 font-bold">
                            <Button className="bg-white shadow-lg">{currentPage}</Button>
                        </li>
                        <li className="mx-1 md:mx-3">
                            <Button onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)} className="bg-white shadow-lg hover:bg-gray-100">Sonraki</Button>
                        </li>
                        <li className="mx-1 md:mx-3">
                            <Button onClick={() => paginate(totalPages)} className="bg-white shadow-lg hover:bg-gray-100">Son</Button>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    );
}