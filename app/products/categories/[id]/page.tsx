'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { db, storage } from '@/config/firebaseConfig';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import Image from 'next/image';

export default function UpdateCategoryPage() {
    const { id } = useParams();
    const [name, setName] = useState('');
    const [image, setImage] = useState<File | null>(null);
    const [existingImageUrl, setExistingImageUrl] = useState<string>('');
    const [preview, setPreview] = useState<string | null>(null);
    const router = useRouter();

    useEffect(() => {
        const fetchCategory = async () => {
            if (typeof id === 'string') {
                const docRef = doc(db, 'categories', id);
                const docSnap = await getDoc(docRef);
                if (docSnap.exists()) {
                    const category = docSnap.data();
                    setName(category.name);
                    setExistingImageUrl(category.imageUrl);
                } else {
                    router.push('/products/categories');
                }
            }
        };

        fetchCategory();
    }, [id, router]);

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setImage(file);
            setPreview(URL.createObjectURL(file));
        }
    };

    const handleRemoveImage = () => {
        setImage(null);
        setPreview(null);
        setExistingImageUrl('');
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        let imageUrl = existingImageUrl;

        if (image) {
            const imageRef = ref(storage, `categories/${image.name}`);
            const snapshot = await uploadBytes(imageRef, image);
            imageUrl = await getDownloadURL(snapshot.ref);
        }

        if (typeof id === 'string') {
            const docRef = doc(db, 'categories', id);
            await updateDoc(docRef, {
                name,
                imageUrl,
            });

            router.push('/products/categories');
        }
    };

    return (
        <div className="p-6 max-w-xl mx-auto">
            <h1 className="text-2xl font-bold mb-4">Kategori Güncelle</h1>
            <form onSubmit={handleSubmit} className="space-y-4">
                <input
                    type="text"
                    placeholder="Kategori Adı"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <div className="space-y-4">
                    <label className="block text-gray-700">Kategori Görseli</label>
                    <div className="border-2 border-dashed border-gray-300 p-4 rounded-lg text-center cursor-pointer hover:bg-gray-50">
                        {preview ? (
                            <div className="relative">
                                <Image
                                    src={preview}
                                    alt="Preview"
                                    fill
                                    className="object-cover rounded-md"
                                    sizes="(max-width: 768px) 100vw, 768px"
                                />
                                <button
                                    type="button"
                                    onClick={handleRemoveImage}
                                    className="absolute top-0 right-0 mt-2 mr-2 bg-red-500 text-white px-2 py-1 rounded-full"
                                >
                                    X
                                </button>
                            </div>
                        ) : (
                            <div>
                                {existingImageUrl && (
                                    <div className="relative">
                                        <Image
                                            src={existingImageUrl}
                                            alt="Existing"
                                            fill
                                            className="object-cover rounded-md"
                                            sizes="(max-width: 768px) 100vw, 768px"
                                        />
                                        <button
                                            type="button"
                                            onClick={handleRemoveImage}
                                            className="absolute top-0 right-0 mt-2 mr-2 bg-red-500 text-white px-2 py-1 rounded-full"
                                        >
                                            X
                                        </button>
                                    </div>
                                )}
                                <input
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageChange}
                                    className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                />
                            </div>
                        )}
                    </div>
                </div>
                <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded-md">Güncelle</button>
            </form>
        </div>
    );
}