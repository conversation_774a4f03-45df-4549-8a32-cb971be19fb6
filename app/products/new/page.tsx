'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { db, storage } from '@/config/firebaseConfig';
import { collection, addDoc, getDoc, doc, getDocs } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import useFirestoreCollection from '@/hooks/useFirestoreCollection';

import Image from 'next/image';

interface Firm {
    id: string;
    name: string;
    commissionRate: number;
}

interface Category {
    id: string;
    name: string;
}

interface Feature {
    name: string;
    price: string;
}

interface OrderDetail {
    id: string;
    orderDate: Date;
    status: string;
    totalPrice: number;
    firmName: string;
    items: Array<{
        name: string;
        price: number;
        quantity: number;
    }>;
}

const validateAndFormatPrice = (price: string): number => {
    const numPrice = Number(price);
    if (isNaN(numPrice)) return 0;
    return numPrice;
};

export default function NewProductPage() {
    const { data: firms } = useFirestoreCollection<Firm>('firms');
    const { data: categories } = useFirestoreCollection<Category>('categories');
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [price, setPrice] = useState('');
    const [purchasePrice, setPurchasePrice] = useState('');

    const [features, setFeatures] = useState<Feature[]>([]);
    const [newFeature, setNewFeature] = useState('');
    const [newFeaturePrice, setNewFeaturePrice] = useState('');
    const [images, setImages] = useState<File[]>([]);
    const [selectedFirm, setSelectedFirm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('');
    const [stockStatus, setStockStatus] = useState('Stokta Var');
    const [discount, setDiscount] = useState('');
    const [barcode, setBarcode] = useState('');
    const router = useRouter();

    // Calculate purchase price when sale price changes
    const calculatePurchasePrice = async (salePrice: string) => {
        if (selectedFirm && salePrice) {
            const docRef = doc(db, 'firms', selectedFirm);
            const docSnap = await getDoc(docRef);
            if (docSnap.exists()) {
                const firm = docSnap.data() as Firm;
                const calculatedPurchasePrice = parseFloat(salePrice) / (1 + firm.commissionRate / 100);
                setPurchasePrice(calculatedPurchasePrice.toFixed(2));
            }
        }
    };

    const handlePriceChange = (value: string) => {
        setPrice(value);
        calculatePurchasePrice(value);
    };

    const handleAddFeature = () => {
        if (newFeature && newFeaturePrice) {
            setFeatures([...features, {
                name: newFeature,
                price: validateAndFormatPrice(newFeaturePrice).toString()
            }]);
            setNewFeature('');
            setNewFeaturePrice('');
        }
    };

    const handleRemoveFeature = (index: number) => {
        const newFeatures = [...features];
        newFeatures.splice(index, 1);
        setFeatures(newFeatures);
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            setImages(Array.from(e.target.files));
        }
    };



    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        const imageUrls: string[] = [];
        for (const image of images) {
            const imageRef = ref(storage, `products/${image.name}`);
            const snapshot = await uploadBytes(imageRef, image);
            const downloadUrl = await getDownloadURL(snapshot.ref);
            imageUrls.push(downloadUrl);
        }

        const validatedFeatures = features.map(feature => ({
            ...feature,
            price: validateAndFormatPrice(feature.price).toString()
        }));

        await addDoc(collection(db, 'products'), {
            name,
            description,
            price: validateAndFormatPrice(price),
            purchasePrice: validateAndFormatPrice(purchasePrice),
            features: validatedFeatures,
            images: imageUrls,
            firmId: selectedFirm,
            category: selectedCategory,
            stockStatus,
            discount: discount ? validateAndFormatPrice(discount) : null,
            barcode,
        });

        router.push('/products');
    };

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto bg-white shadow-xl rounded-xl overflow-hidden">
                <div className="bg-gradient-to-r from-orange-600 to-blue-800 p-6">
                    <h1 className="text-3xl font-bold text-white text-center">Yeni Ürün Ekle</h1>
                </div>
                <form onSubmit={handleSubmit} className="p-8 space-y-6">
                    {/* Firma Selection - Moved to top */}
                    <div className="bg-blue-50 p-4 rounded-lg">
                        <label className="block text-gray-700 font-medium mb-2">Firma Seçimi</label>
                        <select
                            value={selectedFirm}
                            onChange={(e) => setSelectedFirm(e.target.value)}
                            className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white"
                            required
                        >
                            <option value="">Firma Seçin</option>
                            {firms.map(firm => (
                                <option key={firm.id} value={firm.id}>{firm.name}</option>
                            ))}
                        </select>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Ürün Adı</label>
                            <input
                                type="text"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                            />
                        </div>
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Kategori</label>
                            <select
                                value={selectedCategory}
                                onChange={(e) => setSelectedCategory(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                            >
                                <option value="">Kategori Seçin</option>
                                {categories.map(category => (
                                    <option key={category.id} value={category.id}>{category.name}</option>
                                ))}
                            </select>
                        </div>
                    </div>

                    <div>
                        <label className="block text-gray-700 font-medium mb-2">Ürün Açıklaması</label>
                        <textarea
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 min-h-[100px]"
                            required
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Alış Fiyatı</label>
                            <input
                                type="number"
                                value={purchasePrice}
                                onChange={(e) => setPurchasePrice(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                                step="0.01"
                                min="0"
                            />
                        </div>
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Satış Fiyatı</label>
                            <input
                                type="number"
                                value={price}
                                onChange={(e) => handlePriceChange(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                                step="0.01"
                                min="0"
                            />
                        </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg space-y-4">
                        <label className="block text-gray-700 font-medium">Eklenebilir Ek Özellikler</label>
                        <div className="flex items-center space-x-3">
                            <input
                                type="text"
                                placeholder="Özellik Adı"
                                value={newFeature}
                                onChange={(e) => setNewFeature(e.target.value)}
                                className="flex-grow p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-400"
                            />
                            <input
                                type="number"
                                placeholder="Fiyat"
                                value={newFeaturePrice}
                                onChange={(e) => setNewFeaturePrice(e.target.value)}
                                className="w-32 p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-400"
                                step="0.01"
                                min="0"
                            />
                            <button
                                type="button"
                                onClick={handleAddFeature}
                                className="bg-green-500 text-white px-4 py-2 rounded-lg shadow-md hover:bg-green-600 transition-colors"
                            >
                                Ekle
                            </button>
                        </div>
                        <div className="space-y-2">
                            {features.map((feature, index) => (
                                <div key={index} className="flex justify-between items-center bg-white p-3 rounded-lg shadow-sm">
                                    <span>{feature.name} - {feature.price} TL</span>
                                    <button
                                        type="button"
                                        onClick={() => handleRemoveFeature(index)}
                                        className="text-red-500 hover:text-red-700"
                                    >
                                        Kaldır
                                    </button>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="space-y-4">
                        <label className="block text-gray-700 font-medium">Ürün Görselleri</label>
                        <div className="border-2 border-dashed border-gray-300 p-6 rounded-lg text-center hover:bg-gray-50 transition-colors">
                            <input
                                type="file"
                                accept="image/*"
                                multiple
                                onChange={handleImageChange}
                                className="w-full"
                                required
                            />
                            {images.length > 0 && (
                                <div className="mt-4 grid grid-cols-3 gap-4">
                                    {images.map((image, index) => (
                                        <div key={index} className="relative aspect-square">
                                            <Image
                                                src={URL.createObjectURL(image)}
                                                alt={`Preview ${index + 1}`}
                                                fill
                                                className="object-cover rounded-lg shadow-md"
                                                sizes="(max-width: 768px) 33vw, 25vw"
                                                unoptimized
                                            />
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Stok Durumu</label>
                            <select
                                value={stockStatus}
                                onChange={(e) => setStockStatus(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                            >
                                <option value="Stokta Var">Stokta Var</option>
                                <option value="Stokta Yok">Stokta Yok</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">İndirim</label>
                            <input
                                type="number"
                                placeholder="İndirim tutarı"
                                value={discount}
                                onChange={(e) => setDiscount(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                min="0"
                                step="0.01"
                            />
                        </div>
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Barkod</label>
                            <input
                                type="text"
                                placeholder="Barkod"
                                value={barcode}
                                onChange={(e) => setBarcode(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                            />
                        </div>
                    </div>

                    <div className="pt-6">
                        <button
                            type="submit"
                            className="w-full bg-orange-600 text-white px-6 py-3 rounded-lg shadow-lg hover:bg-blue-700 transition-colors font-medium"
                        >
                            Ürünü Ekle
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}