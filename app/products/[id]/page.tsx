'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { db, storage } from '@/config/firebaseConfig';
import { collection, getDocs, doc, getDoc, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

import Image from 'next/image';

interface Feature {
    name: string;
    price: string;
}

interface Firm {
    id: string;
    name: string;
}

interface Category {
    id: string;
    name: string;
}

export default function UpdateProductPage() {
    const { id } = useParams();
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [price, setPrice] = useState('');
    const [purchasePrice, setPurchasePrice] = useState('');

    const [features, setFeatures] = useState<Feature[]>([]);
    const [newFeature, setNewFeature] = useState('');
    const [newFeaturePrice, setNewFeaturePrice] = useState('');
    const [images, setImages] = useState<File[]>([]);
    const [existingImages, setExistingImages] = useState<string[]>([]);
    const [stockStatus, setStockStatus] = useState('');
    const [discount, setDiscount] = useState('');
    const [selectedFirm, setSelectedFirm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('');
    const [firms, setFirms] = useState<Firm[]>([]);
    const [categories, setCategories] = useState<Category[]>([]);
    const [barcode, setBarcode] = useState('');
    const [commissionRate, setCommissionRate] = useState<number>(0);
    const router = useRouter();

    useEffect(() => {
        const fetchProduct = async () => {
            if (typeof id === 'string') {
                const docRef = doc(db, 'products', id);
                const docSnap = await getDoc(docRef);
                if (docSnap.exists()) {
                    const product = docSnap.data();
                    setName(product.name);
                    setDescription(product.description);
                    setPrice(product.price.toString());
                    setPurchasePrice(product.purchasePrice?.toString() || '');
                    setFeatures(product.features || []);
                    setExistingImages(product.images || []);
                    setStockStatus(product.stockStatus);
                    setDiscount(product.discount?.toString() || '');
                    setSelectedFirm(product.firmId);
                    setBarcode(product.barcode || '');
                    setSelectedCategory(product.category);
                } else {
                    router.push('/products');
                }
            }
        };

        const fetchFirms = async () => {
            const querySnapshot = await getDocs(collection(db, 'firms'));
            const firmsList: Firm[] = querySnapshot.docs.map(doc => ({ id: doc.id, name: doc.data().name }));
            setFirms(firmsList);
        };

        const fetchCategories = async () => {
            const querySnapshot = await getDocs(collection(db, 'categories'));
            const categoriesList: Category[] = querySnapshot.docs.map(doc => ({ id: doc.id, name: doc.data().name }));
            setCategories(categoriesList);
        };

        fetchProduct();
        fetchFirms();
        fetchCategories();
    }, [router, id]);

    useEffect(() => {
        const fetchCommissionRate = async () => {
            if (selectedFirm) {
                const docRef = doc(db, 'firms', selectedFirm);
                const docSnap = await getDoc(docRef);
                if (docSnap.exists()) {
                    const firm = docSnap.data();
                    setCommissionRate(firm.commissionRate || 0);
                }
            }
        };

        fetchCommissionRate();
    }, [selectedFirm]);

    // Calculate purchase price when sale price changes
    const calculatePurchasePrice = (salePrice: string) => {
        if (commissionRate && salePrice) {
            const calculatedPurchasePrice = parseFloat(salePrice) / (1 + commissionRate / 100);
            setPurchasePrice(calculatedPurchasePrice.toFixed(2));
        }
    };

    const handlePriceChange = (value: string) => {
        setPrice(value);
        calculatePurchasePrice(value);
    };

    const handleRemoveImage = (index: number) => {
        const updatedImages = [...existingImages];
        updatedImages.splice(index, 1);
        setExistingImages(updatedImages);
    };

    const handleAddFeature = () => {
        if (newFeature && newFeaturePrice) {
            setFeatures([...features, { name: newFeature, price: newFeaturePrice }]);
            setNewFeature('');
            setNewFeaturePrice('');
        }
    };

    const handleRemoveFeature = (index: number) => {
        const newFeatures = [...features];
        newFeatures.splice(index, 1);
        setFeatures(newFeatures);
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            setImages([...images, ...Array.from(e.target.files)]);
        }
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        const imageUrls = [...existingImages];

        for (const image of images) {
            const imageRef = ref(storage, `products/${image.name}`);
            const snapshot = await uploadBytes(imageRef, image);
            const imageUrl = await getDownloadURL(snapshot.ref);
            imageUrls.push(imageUrl);
        }

        if (typeof id === 'string') {
            const docRef = doc(db, 'products', id);
            await updateDoc(docRef, {
                name,
                description,
                price: parseFloat(price),
                purchasePrice: parseFloat(purchasePrice),
                features,
                images: imageUrls,
                stockStatus,
                discount: discount ? parseFloat(discount) : null,
                firmId: selectedFirm,
                category: selectedCategory,
                barcode,
            });

            router.push('/products');
        }
    };



    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-4xl mx-auto bg-white shadow-xl rounded-xl overflow-hidden">
                <div className="bg-gradient-to-r from-orange-600 to-blue-800 p-6">
                    <h1 className="text-3xl font-bold text-white text-center">Ürünü Güncelle</h1>
                </div>
                <form onSubmit={handleSubmit} className="p-8 space-y-6">
                    {/* Firma Selection - At the top */}
                    <div className="bg-blue-50 p-4 rounded-lg">
                        <label className="block text-gray-700 font-medium mb-2">Firma Seçimi</label>
                        <select
                            value={selectedFirm}
                            onChange={(e) => setSelectedFirm(e.target.value)}
                            className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white"
                            required
                        >
                            <option value="">Firma Seçin</option>
                            {firms.map(firm => (
                                <option key={firm.id} value={firm.id}>{firm.name}</option>
                            ))}
                        </select>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Ürün Adı</label>
                            <input
                                type="text"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                            />
                        </div>
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Kategori</label>
                            <select
                                value={selectedCategory}
                                onChange={(e) => setSelectedCategory(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                            >
                                <option value="">Kategori Seçin</option>
                                {categories.map(category => (
                                    <option key={category.id} value={category.id}>{category.name}</option>
                                ))}
                            </select>
                        </div>
                    </div>

                    <div>
                        <label className="block text-gray-700 font-medium mb-2">Ürün Açıklaması</label>
                        <textarea
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 min-h-[100px]"
                            required
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Alış Fiyatı</label>
                            <input
                                type="number"
                                value={purchasePrice}
                                onChange={(e) => setPurchasePrice(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                                step="0.01"
                                min="0"
                            />
                        </div>
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Satış Fiyatı</label>
                            <input
                                type="number"
                                value={price}
                                onChange={(e) => handlePriceChange(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                                step="0.01"
                                min="0"
                            />
                        </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg space-y-4">
                        <label className="block text-gray-700 font-medium">Eklenebilir Ek Özellikler</label>
                        <div className="flex items-center space-x-3">
                            <input
                                type="text"
                                placeholder="Özellik Adı"
                                value={newFeature}
                                onChange={(e) => setNewFeature(e.target.value)}
                                className="flex-grow p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-400"
                            />
                            <input
                                type="number"
                                placeholder="Fiyat"
                                value={newFeaturePrice}
                                onChange={(e) => setNewFeaturePrice(e.target.value)}
                                className="w-32 p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-400"
                                step="0.01"
                                min="0"
                            />
                            <button
                                type="button"
                                onClick={handleAddFeature}
                                className="bg-green-500 text-white px-4 py-2 rounded-lg shadow-md hover:bg-green-600 transition-colors"
                            >
                                Ekle
                            </button>
                        </div>
                        <div className="space-y-2">
                            {features.map((feature, index) => (
                                <div key={index} className="flex justify-between items-center bg-white p-3 rounded-lg shadow-sm">
                                    <span>{feature.name} - {feature.price} TL</span>
                                    <button
                                        type="button"
                                        onClick={() => handleRemoveFeature(index)}
                                        className="text-red-500 hover:text-red-700"
                                    >
                                        Kaldır
                                    </button>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="space-y-4">
                        <label className="block text-gray-700 font-medium">Ürün Görselleri</label>
                        <div className="border-2 border-dashed border-gray-300 p-6 rounded-lg text-center hover:bg-gray-50 transition-colors">
                            <input
                                type="file"
                                accept="image/*"
                                multiple
                                onChange={handleImageChange}
                                className="w-full"
                            />
                            <div className="mt-4 grid grid-cols-3 gap-4">
                                {existingImages.map((image, index) => (
                                    <div key={`existing-${index}`} className="relative aspect-square">
                                        <Image
                                            src={image}
                                            alt={`Existing ${index + 1}`}
                                            fill
                                            className="object-cover rounded-lg shadow-md"
                                            sizes="(max-width: 768px) 100vw, 768px"
                                        />
                                        <button
                                            type="button"
                                            onClick={() => handleRemoveImage(index)}
                                            className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
                                        >
                                            ×
                                        </button>
                                    </div>
                                ))}
                                {images.map((image, index) => (
                                    <div key={`new-${index}`} className="relative aspect-square">
                                        <Image
                                            src={URL.createObjectURL(image)}
                                            alt={`New ${index + 1}`}
                                            fill
                                            className="object-cover rounded-lg shadow-md"
                                            sizes="(max-width: 768px) 100vw, 768px"
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Stok Durumu</label>
                            <select
                                value={stockStatus}
                                onChange={(e) => setStockStatus(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                required
                            >
                                <option value="Stokta Var">Stokta Var</option>
                                <option value="Stokta Yok">Stokta Yok</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">İndirim</label>
                            <input
                                type="number"
                                placeholder="İndirim tutarı"
                                value={discount}
                                onChange={(e) => setDiscount(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                min="0"
                                step="0.01"
                            />
                        </div>
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">Barkod</label>
                            <input
                                type="text"
                                placeholder="Barkod"
                                value={barcode}
                                onChange={(e) => setBarcode(e.target.value)}
                                className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                            />
                        </div>
                    </div>

                    <div className="pt-6">
                        <button
                            type="submit"
                            className="w-full bg-orange-600 text-white px-6 py-3 rounded-lg shadow-lg hover:bg-blue-700 transition-colors font-medium"
                        >
                            Ürünü Güncelle
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}