'use client';

import { useEffect, useState } from 'react';
import { db } from '@/config/firebaseConfig';
import { collection, query, where, onSnapshot } from 'firebase/firestore';
import { Search, Users2 } from 'lucide-react';

interface User {
    firstName: string;
    lastName: string;
    phoneNumber: string;
}

export default function UsersPage() {
    const [users, setUsers] = useState<User[]>([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [isLoading, setIsLoading] = useState(true);

    // Fetch users from Firestore with real-time updates
    useEffect(() => {
        const q = query(collection(db, 'users'), where('role', '==', 'user'));
        const unsubscribe = onSnapshot(q, (querySnapshot) => {
            const usersList = querySnapshot.docs.map(doc => ({
                firstName: doc.data().firstName,
                lastName: doc.data().lastName,
                phoneNumber: doc.data().phoneNumber
            } as User));
            setUsers(usersList);
            setIsLoading(false);
        });

        return () => unsubscribe();
    }, []);

    // Filter users based on search term
    const filteredUsers = users.filter(user => {
        if (!searchTerm) return true;
        
        const searchLower = searchTerm.toLowerCase();
        const firstName = user.firstName || '';
        const lastName = user.lastName || '';
        const phone = user.phoneNumber || '';

        return (
            firstName.toLowerCase().includes(searchLower) ||
            lastName.toLowerCase().includes(searchLower) ||
            phone.includes(searchTerm)
        );
    });

    return (
        <div className="p-8 bg-gray-50 min-h-screen">
            {/* Header Section */}
            <div className="mb-8">
                <div className="flex items-center gap-3 mb-2">
                    <Users2 className="w-8 h-8 text-indigo-600" />
                    <h1 className="text-2xl font-bold text-gray-800">Müşteriler</h1>
                </div>
                <div className="flex items-center justify-between flex-wrap gap-4">
                    <div className="flex items-center gap-2 text-gray-600">
                        <span className="text-2xl font-semibold text-indigo-600">
                            {users.length}
                        </span>
                        <span>kayıtlı müşteri</span>
                    </div>

                    {/* Search Bar */}
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                            type="text"
                            placeholder="Müşteri ara..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent w-64 bg-white"
                        />
                    </div>
                </div>
            </div>

            {/* Table Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr className="bg-gray-50">
                                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Ad
                                </th>
                                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Soyad
                                </th>
                                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Telefon Numarası
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {isLoading ? (
                                <tr>
                                    <td colSpan={3} className="px-6 py-4 text-center text-gray-500">
                                        Yükleniyor...
                                    </td>
                                </tr>
                            ) : filteredUsers.length === 0 ? (
                                <tr>
                                    <td colSpan={3} className="px-6 py-4 text-center text-gray-500">
                                        {searchTerm ? 'Aranan kriterlere uygun müşteri bulunamadı.' : 'Henüz müşteri kaydı bulunmuyor.'}
                                    </td>
                                </tr>
                            ) : (
                                filteredUsers.map((user, index) => (
                                    <tr 
                                        key={index} 
                                        className="hover:bg-gray-50 transition-colors duration-150"
                                    >
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {user.firstName}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            {user.lastName}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                            {user.phoneNumber}
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}