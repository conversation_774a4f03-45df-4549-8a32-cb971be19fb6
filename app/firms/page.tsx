'use client';

import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { deleteDoc, doc } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';
import Link from 'next/link';
import { useFirestoreCollection } from '@/hooks/useFirestoreCollection';
import DeleteModal from '@/components/DeleteModal';
import FirmRow from '@/components/FirmRow';
import Button from '@/components/Button';
import { Building2, Plus, Search, ChevronFirst, ChevronLast, ChevronLeft, ChevronRight } from 'lucide-react';

interface Firm {
    id: string;
    name: string;
    email: string;
    description: string;
    banner: string;
    visible: boolean;
}

export default function FirmsPage() {
    const { data: firms, loading, setData: setFirms } = useFirestoreCollection<Firm>('firms');
    const [showModal, setShowModal] = useState(false);
    const [firmToDelete, setFirmToDelete] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [firmsPerPage] = useState<number>(20);
    const [searchTerm, setSearchTerm] = useState<string>('');
    useRouter();

    const confirmDelete = (id: string) => {
        setFirmToDelete(id);
        setShowModal(true);
    };

    const handleDelete = async () => {
        if (firmToDelete) {
            await deleteDoc(doc(db, 'firms', firmToDelete));
            setFirms((prevFirms) => prevFirms.filter((firm) => firm.id !== firmToDelete));
            setShowModal(false);
            setFirmToDelete(null);
        }
    };

    const filteredFirms = useMemo(() => {
        return firms.filter(firm => {
            const normalizedSearchTerm = searchTerm
                .normalize("NFD")
                .replace(/[\u0300-\u036f]/g, "");
            
            const normalizedFirmName = firm.name
                .normalize("NFD")
                .replace(/[\u0300-\u036f]/g, "");
            
            return searchTerm ? normalizedFirmName.toUpperCase().includes(normalizedSearchTerm.toUpperCase()) : true;
        });
    }, [firms, searchTerm]);

    const indexOfLastFirm = currentPage * firmsPerPage;
    const indexOfFirstFirm = indexOfLastFirm - firmsPerPage;
    const currentFirms = filteredFirms.slice(indexOfFirstFirm, indexOfLastFirm);
    const totalPages = Math.ceil(filteredFirms.length / firmsPerPage);

    const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
            </div>
        );
    }

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Header Section */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div className="flex items-center gap-3">
                        <Building2 className="w-8 h-8 text-orange-600" />
                        <h1 className="text-2xl font-bold text-gray-800">Firmalar</h1>
                    </div>

                    <div className="flex items-center gap-4">
                        <div className="relative flex-1 sm:flex-none sm:w-64">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                            <input
                                type="text"
                                placeholder="Firma Ara"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10 pr-4 py-2 w-full border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm bg-white shadow-sm"
                            />
                        </div>

                        <Link href="/firms/new">
                            <Button className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 shadow-sm">
                                <Plus className="w-4 h-4" />
                                <span>Yeni Firma</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Table Section */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr className="bg-gray-50">
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Logo</th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Firma Adı</th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Açıklama</th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Eylem</th>
                                    <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Görünürlük</th>
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200 bg-white">
                                {currentFirms.length === 0 ? (
                                    <tr>
                                        <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                                            {searchTerm ? 'Arama sonucuna uygun firma bulunamadı.' : 'Henüz firma bulunmuyor.'}
                                        </td>
                                    </tr>
                                ) : (
                                    currentFirms.map((firm) => (
                                        <FirmRow key={firm.id} firm={firm} onDelete={confirmDelete} />
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Pagination */}
          {/* Pagination */}
{totalPages > 1 && (
    <div className="flex justify-center">
        <div className="flex items-center gap-2 bg-white px-4 py-2 rounded-lg shadow-sm border border-gray-200">
            <Button 
                onClick={() => paginate(1)} 
                className={`p-2 rounded-lg ${
                    currentPage === 1 
                    ? 'text-gray-400 cursor-not-allowed' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
            >
                <ChevronFirst className="w-4 h-4" />
            </Button>
            <Button 
                onClick={() => currentPage > 1 && paginate(currentPage - 1)}
                className={`p-2 rounded-lg ${
                    currentPage === 1 
                    ? 'text-gray-400 cursor-not-allowed' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
            >
                <ChevronLeft className="w-4 h-4" />
            </Button>
            <span className="px-4 text-sm font-medium text-gray-700">
                Sayfa {currentPage} / {totalPages}
            </span>
            <Button 
                onClick={() => currentPage < totalPages && paginate(currentPage + 1)}
                className={`p-2 rounded-lg ${
                    currentPage === totalPages 
                    ? 'text-gray-400 cursor-not-allowed' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
            >
                <ChevronRight className="w-4 h-4" />
            </Button>
            <Button 
                onClick={() => paginate(totalPages)}
                className={`p-2 rounded-lg ${
                    currentPage === totalPages 
                    ? 'text-gray-400 cursor-not-allowed' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
            >
                <ChevronLast className="w-4 h-4" />
            </Button>
        </div>
    </div>
)}

                {showModal && <DeleteModal onClose={() => setShowModal(false)} onDelete={handleDelete} />}
            </div>
        </div>
    );
}