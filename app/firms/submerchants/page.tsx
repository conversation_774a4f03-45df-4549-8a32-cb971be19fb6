'use client';
import { useState, useEffect } from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';

interface SubMerchant {
    id: string;
    name: string;
    email: string;
    gsmNumber: string;
    address: string;
    iban: string;
    identityNumber: string;
    subMerchantExternalId: string;
    subMerchantType: string;
    legalCompanyTitle: string;
}

export default function ListSubMerchantsPage() {
    const [subMerchants, setSubMerchants] = useState<SubMerchant[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        const fetchSubMerchants = async () => {
            try {
                const querySnapshot = await getDocs(collection(db, 'subMerchants'));
                const subMerchantsList = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as SubMerchant));
                setSubMerchants(subMerchantsList);
            } catch (err) {
                setError('Veriler alınırken bir hata oluştu.');
            } finally {
                setLoading(false);
            }
        };

        fetchSubMerchants();
    }, []);

    if (loading) {
        return <p>Yükleniyor...</p>;
    }

    if (error) {
        return <p>{error}</p>;
    }

    return (
        <div className="container mx-auto py-10 px-4">
            <h1 className="text-3xl font-bold mb-6">Alt Üye İşyeri Listesi</h1>
            <table className="min-w-full bg-white">
                <thead>
                    <tr>
                        <th className="py-2">İşyeri Adı</th>
                        <th className="py-2">Email</th>
                        <th className="py-2">Telefon Numarası</th>
                        <th className="py-2">Adres</th>
                        <th className="py-2">IBAN</th>
                        <th className="py-2">TC Kimlik No</th>
                        <th className="py-2">Dış ID</th>
                        <th className="py-2">Üye Tipi</th>
                        <th className="py-2">Yasal Şirket Ünvanı</th>
                    </tr>
                </thead>
                <tbody>
                    {subMerchants.map(subMerchant => (
                        <tr key={subMerchant.id}>
                            <td className="border px-4 py-2">{subMerchant.name}</td>
                            <td className="border px-4 py-2">{subMerchant.email}</td>
                            <td className="border px-4 py-2">{subMerchant.gsmNumber}</td>
                            <td className="border px-4 py-2">{subMerchant.address}</td>
                            <td className="border px-4 py-2">{subMerchant.iban}</td>
                            <td className="border px-4 py-2">{subMerchant.identityNumber}</td>
                            <td className="border px-4 py-2">{subMerchant.subMerchantExternalId}</td>
                            <td className="border px-4 py-2">{subMerchant.subMerchantType}</td>
                            <td className="border px-4 py-2">{subMerchant.legalCompanyTitle}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}