'use client';
import { useState, useEffect } from 'react';
import axios, { AxiosError } from 'axios';
import { onAuthStateChanged } from 'firebase/auth';
import { auth, db } from '@/config/firebaseConfig';
import { doc, setDoc } from 'firebase/firestore';

export default function CreateSubMerchantPage() {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        gsmNumber: '',
        address: '',
        iban: '',
        identityNumber: '',
        subMerchantExternalId: '',
        subMerchantType: 'PRIVATE_COMPANY',
        legalCompanyTitle: '',
    });

    const [subMerchantKey, setSubMerchantKey] = useState('');
    const [errorMessage, setErrorMessage] = useState('');
    const [userToken, setUserToken] = useState('');

    const API_KEY = 'r68GaxwjnaL7I0wt2oYP4XT7TV8QaINa';
    const SECRET_KEY = 'VsXrQ6S6p9RL5E73C0BtmkAW6D7pV9Fj';
    const BASE_URL = 'https://api.iyzipay.com';

    useEffect(() => {
        onAuthStateChanged(auth, async (user) => {
            if (user) {
                const token = await user.getIdToken();
                setUserToken(token);
            } else {
                setUserToken('');
            }
        });
    }, []);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLSelectElement>) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value,
        });
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setErrorMessage('');
        setSubMerchantKey('');

        if (!userToken) {
            setErrorMessage('Lütfen giriş yapın.');
            return;
        }

        try {
            const response = await axios.post(
                `${BASE_URL}/onboarding/submerchant`,
                formData,
                {
                    headers: {
                        'Authorization': `Basic ${Buffer.from(`${API_KEY}:${SECRET_KEY}`).toString('base64')}`,
                    },
                }
            );

            const subMerchantKey = response.data.subMerchantKey;
            setSubMerchantKey(subMerchantKey);

            await setDoc(doc(db, 'subMerchants', subMerchantKey), {
                ...formData,
                subMerchantKey,
                createdAt: new Date(),
            });
        } catch (error: AxiosError | any) {
            setErrorMessage(error.response?.data?.message || 'Bir hata oluştu.');
        }
    };

    return (
        <div className="container mx-auto py-10 px-4">
            <h1 className="text-3xl font-bold mb-6">Alt Üye İşyeri Oluştur</h1>
            {subMerchantKey && (
                <p className="text-green-500 mb-4">
                    Alt Üye Başarıyla Oluşturuldu! SubMerchantKey: {subMerchantKey}
                </p>
            )}
            {errorMessage && <p className="text-red-500 mb-4">{errorMessage}</p>}

            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <label className="block text-gray-700">İşyeri Adı</label>
                    <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
                        required
                    />
                </div>
                <div>
                    <label className="block text-gray-700">Email</label>
                    <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
                        required
                    />
                </div>
                <div>
                    <label className="block text-gray-700">Telefon Numarası</label>
                    <input
                        type="text"
                        name="gsmNumber"
                        value={formData.gsmNumber}
                        onChange={handleInputChange}
                        className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
                        required
                    />
                </div>
                <div>
                    <label className="block text-gray-700">Adres</label>
                    <input
                        type="text"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
                        required
                    />
                </div>
                <div>
                    <label className="block text-gray-700">IBAN</label>
                    <input
                        type="text"
                        name="iban"
                        value={formData.iban}
                        onChange={handleInputChange}
                        className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
                        required
                    />
                </div>
                <div>
                    <label className="block text-gray-700">TC Kimlik No</label>
                    <input
                        type="text"
                        name="identityNumber"
                        value={formData.identityNumber}
                        onChange={handleInputChange}
                        className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
                        required
                    />
                </div>
                <div>
                    <label className="block text-gray-700">Dış ID</label>
                    <input
                        type="text"
                        name="subMerchantExternalId"
                        value={formData.subMerchantExternalId}
                        onChange={handleInputChange}
                        className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
                        required
                    />
                </div>
                <div>
                    <label className="block text-gray-700">Üye Tipi</label>
                    <select
                        name="subMerchantType"
                        value={formData.subMerchantType}
                        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleInputChange(e)}
                        className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
                    >
                        <option value="PRIVATE_COMPANY">Şahıs Şirketi</option>
                        <option value="LIMITED_OR_JOINT_STOCK_COMPANY">Limited / Anonim Şirketi</option>
                    </select>
                </div>
                <div>
                    <label className="block text-gray-700">Yasal Şirket Ünvanı</label>
                    <input
                        type="text"
                        name="legalCompanyTitle"
                        value={formData.legalCompanyTitle}
                        onChange={handleInputChange}
                        className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm"
                        required
                    />
                </div>
                <button
                    type="submit"
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md shadow hover:bg-blue-700 transition"
                >
                    Alt Üye Oluştur
                </button>
            </form>
        </div>
    );
}