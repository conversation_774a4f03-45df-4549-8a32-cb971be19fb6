'use client';

import {useState, useEffect} from 'react';
import {useRouter, useParams} from 'next/navigation';
import {db, storage} from '@/config/firebaseConfig';
import {
    doc,
    getDoc,
    updateDoc,
    collection,
    getDocs
} from 'firebase/firestore';
import {
    ref,
    uploadBytes,
    getDownloadURL
} from 'firebase/storage';
import Select, {MultiValue} from 'react-select';
import Image from 'next/image';

type CategoryOption = {
    value: string;
    label: string;
};

export default function UpdateFirmPage() {
    const {id} = useParams();
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [description, setDescription] = useState('');
    const [banner, setBanner] = useState<File | null>(null);
    const [existingBannerUrl, setExistingBannerUrl] = useState<string>('');
    const [preview, setPreview] = useState<string | null>(null);
    const [categories, setCategories] = useState<CategoryOption[]>([]);
    const [selectedCategories, setSelectedCategories] = useState<MultiValue<CategoryOption>>([]);
    const [categoriesLoaded, setCategoriesLoaded] = useState(false);
    const [visible, setVisible] = useState<boolean>(true);
    const [subMerchantKey, setSubMerchantKey] = useState('');
    const [commissionRate, setCommissionRate] = useState<number>(0);
    const router = useRouter();

    useEffect(() => {
        const fetchCategories = async () => {
            const querySnapshot = await getDocs(collection(db, 'categories'));
            const categoriesData = querySnapshot.docs.map(doc => ({
                value: doc.id,
                label: doc.data().name,
            }));
            setCategories(categoriesData);
            setCategoriesLoaded(true);
        };

        fetchCategories();
    }, []);

    useEffect(() => {
        const fetchFirm = async () => {
            if (categoriesLoaded && typeof id === 'string') {
                const docRef = doc(db, 'firms', id);
                const docSnap = await getDoc(docRef);
                if (docSnap.exists()) {
                    const firm = docSnap.data();
                    setName(firm.name);
                    setEmail(firm.email);
                    setDescription(firm.description);
                    setExistingBannerUrl(firm.banner);
                    setVisible(firm.visible);
                    setSubMerchantKey(firm.subMerchantKey);
                    setCommissionRate(firm.commissionRate || 0);

                    const selectedCategoriesData = firm.categories.map((categoryId: string) => {
                        const category = categories.find(cat => cat.value === categoryId);
                        return category ? {value: category.value, label: category.label} : null;
                    }).filter(Boolean) as CategoryOption[];
                    setSelectedCategories(selectedCategoriesData);
                } else {
                    router.push('/firms');
                }
            }
        };

        if (categoriesLoaded) {
            fetchFirm();
        }
    }, [categoriesLoaded, id, categories, router]);

    const handleBannerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setBanner(file);
            setPreview(URL.createObjectURL(file));
        }
    };

    const handleRemoveImage = () => {
        setBanner(null);
        setPreview(null);
        setExistingBannerUrl('');
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        let bannerUrl = existingBannerUrl;

        if (banner) {
            const bannerRef = ref(storage, `firms/${banner.name}`);
            const snapshot = await uploadBytes(bannerRef, banner);
            bannerUrl = await getDownloadURL(snapshot.ref);
        }

        try {
            if (typeof id === 'string') {
                const docRef = doc(db, 'firms', id);
                await updateDoc(docRef, {
                    name,
                    email,
                    description,
                    banner: bannerUrl,
                    categories: selectedCategories.map(category => category.value),
                    visible,
                    subMerchantKey: subMerchantKey || '', // Ensure subMerchantKey is not undefined
                    commissionRate,
                });

                router.push('/firms');
            }
        } catch (error) {
            console.error("Firma güncellenemedi:", error);
        }
    };

    return (
        <div className="p-6 max-w-2xl mx-auto bg-white shadow-md rounded-lg">
            <h1 className="text-3xl font-bold mb-6 text-center text-gray-800">Firmayı Güncelle</h1>
            <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                    <label className="block text-gray-700 font-medium">Firma Adı</label>
                    <input
                        type="text"
                        placeholder="Firma Adı"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                    />
                </div>
                <div>
                    <label className="block text-gray-700 font-medium">Firma Email</label>
                    <input
                        type="email"
                        placeholder="Firma Email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                    />
                </div>
                <div>
                    <label className="block text-gray-700 font-medium">Firma Açıklaması</label>
                    <textarea
                        placeholder="Firma Açıklaması"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                    />
                </div>
                <div>
                    <label className="block text-gray-700 font-medium">Üye İşyeri ID</label>
                    <input
                        type="text"
                        placeholder="Üye İşyeri ID"
                        value={subMerchantKey}
                        onChange={(e) => setSubMerchantKey(e.target.value)}
                        className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                    />
                </div>
                <div>
                    <label className="block text-gray-700 font-medium">Komisyon Oranı</label>
                    <input
                        type="number"
                        placeholder="Komisyon Oranı"
                        value={commissionRate}
                        onChange={(e) => setCommissionRate(parseFloat(e.target.value))}
                        className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                    />
                </div>
                <div className="space-y-4">
                    <label className="block text-gray-700 font-medium">Firma Bannerı</label>
                    <div
                        className="border-2 border-dashed border-gray-300 p-4 rounded-lg text-center cursor-pointer hover:bg-gray-50">
                        {preview ? (
                            <div className="relative">
                                <Image
                                    src={preview}
                                    alt="Preview"
                                    fill
                                    className="object-cover rounded-md mx-auto"
                                    sizes="(max-width: 768px) 100vw, 768px"
                                />
                                <button
                                    type="button"
                                    onClick={handleRemoveImage}
                                    className="absolute top-0 right-0 mt-2 mr-2 bg-red-500 text-white px-2 py-1 rounded-full"
                                >
                                    X
                                </button>
                            </div>
                        ) : (
                            <div>
                                {existingBannerUrl && (
                                    <div className="relative">
                                        <Image
                                            src={existingBannerUrl}
                                            alt={`${name} banner`}
                                            fill
                                            className="object-cover rounded-lg mb-4"
                                            sizes="(max-width: 768px) 100vw, 768px"
                                        />
                                        <button
                                            type="button"
                                            onClick={handleRemoveImage}
                                            className="absolute top-0 right-0 mt-2 mr-2 bg-red-500 text-white px-2 py-1 rounded-full"
                                        >
                                            X
                                        </button>
                                    </div>
                                )}
                                <input
                                    type="file"
                                    accept="image/*"
                                    onChange={handleBannerChange}
                                    className="w-full p-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                                />
                            </div>
                        )}
                    </div>
                </div>
                <div className="space-y-4">
                    <label className="block text-gray-700 font-medium">Ana Kategoriler</label>
                    <Select
                        isMulti
                        options={categories}
                        value={selectedCategories}
                        onChange={setSelectedCategories}
                        className="w-full"
                    />
                </div>
                <div className="space-y-4">
                    <label className="block text-gray-700 font-medium">Görünürlük</label>
                    <div className="flex items-center">
                        <label className="relative inline-flex items-center cursor-pointer">
                            <input
                                type="checkbox"
                                checked={visible}
                                onChange={(e) => setVisible(e.target.checked)}
                                className="sr-only peer"
                            />
                            <div
                                className="w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
                            <span
                                className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">{visible ? 'Görünür' : 'Görünmez'}</span>
                        </label>
                    </div>
                </div>
                <button type="submit"
                        className="bg-blue-500 text-white px-5 py-3 rounded-lg shadow-lg hover:bg-blue-600 transition-all">
                    Güncelle
                </button>
            </form>
        </div>
    );
}