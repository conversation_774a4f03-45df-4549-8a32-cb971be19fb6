'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { db, storage } from '@/config/firebaseConfig';
import { collection, addDoc, doc, setDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { getAuth, createUserWithEmailAndPassword } from 'firebase/auth';
import Image from 'next/image';

export default function NewFirmPage() {
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [description, setDescription] = useState('');
    const [iban, setIban] = useState('');
    const [taxNumber, setTaxNumber] = useState('');
    const [taxOffice, setTaxOffice] = useState('');
    const [banner, setBanner] = useState<File | null>(null);
    const [preview, setPreview] = useState<string | null>(null);
    const router = useRouter();
    const auth = getAuth();

    const handleBannerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setBanner(file);
            setPreview(URL.createObjectURL(file));
        }
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        try {
            // Firebase Authentication'da yeni kullanıcı oluştur
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;

            let bannerUrl = '';

            if (banner) {
                const bannerRef = ref(storage, `firms/${banner.name}`);
                const snapshot = await uploadBytes(bannerRef, banner);
                bannerUrl = await getDownloadURL(snapshot.ref);
            }

            // Firmanın bilgilerini Firestore'a ekle
            await addDoc(collection(db, 'firms'), {
                name,
                email,
                description,
                iban,
                taxNumber,
                taxOffice,
                banner: bannerUrl,
                userId: user.uid,
            });

            // Kullanıcıya "firm" rolü atama
            await setDoc(doc(db, "users", user.uid), {
                role: "firm",
                email,
                name,
            });

            console.log("Firma ve kullanıcı başarıyla eklendi!");
            router.push('/firms');
        } catch (error) {
            console.error("Hata:", error);
        }
    };

    return (
        <div className="p-6 max-w-2xl mx-auto">
            <h1 className="text-2xl font-bold mb-4">Yeni Firma Ekle</h1>
            <form onSubmit={handleSubmit} className="space-y-4">
                <input
                    type="text"
                    placeholder="Firma Adı"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="email"
                    placeholder="Firma Email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="password"
                    placeholder="Firma Şifre"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <textarea
                    placeholder="Firma Açıklaması"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="text"
                    placeholder="IBAN"
                    value={iban}
                    onChange={(e) => setIban(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="text"
                    placeholder="Vergi Numarası"
                    value={taxNumber}
                    onChange={(e) => setTaxNumber(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <input
                    type="text"
                    placeholder="Vergi Dairesi"
                    value={taxOffice}
                    onChange={(e) => setTaxOffice(e.target.value)}
                    className="w-full p-2 border rounded-md"
                />
                <div className="border-2 border-dashed border-gray-300 p-4 rounded-md text-center cursor-pointer hover:bg-gray-50">
                    {preview ? (
                        <div className="relative w-full h-48">
                            <Image
                                src={preview}
                                alt="Preview"
                                fill
                                className="object-contain rounded-md"
                                sizes="(max-width: 768px) 100vw, 768px"
                            />
                            <button
                                type="button"
                                onClick={() => {
                                    setBanner(null);
                                    setPreview(null);
                                }}
                                className="absolute top-0 right-0 mt-2 mr-2 bg-red-500 text-white px-2 py-1 rounded-full z-10"
                            >
                                X
                            </button>
                        </div>
                    ) : (
                        <p className="text-gray-500">Görsel seçmek için dosyayı sürükleyip bırakın veya tıklayın</p>
                    )}
                    <input
                        type="file"
                        accept="image/*"
                        onChange={handleBannerChange}
                        className="w-full p-2 border rounded-md mt-2"
                    />
                </div>
                <button type="submit" className="bg-green-500 text-white px-4 py-2 rounded-md mt-4">Ekle</button>
            </form>
        </div>
    );
}
