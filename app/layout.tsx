'use client';

import './globals.css';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
    FaUser,
    FaBuilding,
    FaTachometerAlt,
    FaMotorcycle,
    FaBoxOpen,
    FaStar,
    FaChartBar,
    FaShoppingCart,
    FaImages,
    FaSignOutAlt,
    FaChevronDown,
    FaInfoCircle
} from 'react-icons/fa';
import Image from 'next/image';
import { auth } from '@/config/firebaseConfig';
import { ReactNode, useEffect, useState } from 'react';
import { getAuth, onAuthStateChanged } from 'firebase/auth';

interface LayoutProps {
    children: ReactNode;
}

interface MenuItem {
    path: string;
    icon: typeof FaUser;
    label: string;
}

export default function Layout({ children }: LayoutProps) {
    const router = useRouter();
    const pathname = usePathname();
    const [showChanges, setShowChanges] = useState(false);
    const [isNewVersion, setIsNewVersion] = useState(true);
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [userName, setUserName] = useState<string | null>(null);
    const [isMobile, setIsMobile] = useState(false);
    const [showMobileMenu, setShowMobileMenu] = useState(false);

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
            if (window.innerWidth >= 768) {
                setShowMobileMenu(false);
            }
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    useEffect(() => {
        const auth = getAuth();
        const unsubscribe = onAuthStateChanged(auth, (user) => {
            if (!user && pathname !== '/login') {
                router.push('/login');
            } else if (user) {
                setUserName(user.displayName || user.email?.split('@')[0] || 'Admin');
            }
        });

        return () => unsubscribe();
    }, [router, pathname]);

    if (pathname === '/login') {
        return <>{children}</>;
    }

    const menuItems: MenuItem[] = [
        { path: '/admin', icon: FaTachometerAlt, label: 'Dashboard' },
        { path: '/sliders', icon: FaImages, label: 'Slaytlar' },
        { path: '/users', icon: FaUser, label: 'Müşteriler' },
        { path: '/firms', icon: FaBuilding, label: 'Firma Yönetimi' },
        { path: '/couriers', icon: FaMotorcycle, label: 'Kuryeler' },
        { path: '/orders', icon: FaBoxOpen, label: 'Siparişler' },
        { path: '/products', icon: FaShoppingCart, label: 'Ürünler' },
        { path: '/ratings', icon: FaStar, label: 'Değerlendirmeler' },
        { path: '/statics', icon: FaChartBar, label: 'İstatistikler' }
    ];

    const recentUpdates = [
     
        'Ürün Sayfası Sıralama Sürükle-Bırak Eklendi',
    ];

    return (
        <html lang="tr">
        <head>
            <title>Master Admin Panel - Yemek Kapımda</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        </head>
        <body className="bg-gray-50">
            <div className="flex min-h-screen">
                {/* Mobile Header */}
                {isMobile && (
                    <header className="fixed top-0 left-0 right-0 bg-white z-50 border-b border-gray-200">
                        <div className="flex items-center justify-between px-4 py-3">
                            <div className="flex items-center space-x-3">
                                <Image src="/app_logo.png" alt="Logo" width={32} height={32} className="rounded" />
                                <span className="font-semibold text-gray-800">Admin Panel</span>
                            </div>
                            <button
                                onClick={() => setShowMobileMenu(!showMobileMenu)}
                                className="text-gray-500 hover:text-gray-700"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    {showMobileMenu
                                        ? <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        : <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                    }
                                </svg>
                            </button>
                        </div>
                    </header>
                )}

                {/* Sidebar */}
                <aside
                    className={`
                        ${isMobile 
                            ? `fixed inset-y-0 left-0 transform ${showMobileMenu ? 'translate-x-0' : '-translate-x-full'}`
                            : `${isCollapsed ? 'w-20' : 'w-72'} fixed`
                        }
                        h-screen
                        bg-gradient-to-b from-orange-600 to-orange-700 
                        text-white shadow-xl
                        transition-all duration-300 ease-in-out z-30
                        flex flex-col
                    `}
                >
                    {/* Sidebar Header */}
                    <div className={`
                        ${isCollapsed ? 'justify-center' : 'justify-between'} 
                        flex items-center p-6 border-b border-orange-500/30
                        flex-shrink-0
                    `}>
                        <div className="flex items-center space-x-3">
                            <Image
                                src="/app_logo.png"
                                alt="Logo"
                                width={40}
                                height={40}
                                className="rounded-lg shadow-lg"
                            />
                            {!isCollapsed && (
                                <h2 className="text-xl font-bold tracking-wide">Admin Panel</h2>
                            )}
                        </div>
                        {!isMobile && (
                            <button
                                onClick={() => setIsCollapsed(!isCollapsed)}
                                className="text-orange-200 hover:text-white transition-colors"
                            >
                                <FaChevronDown
                                    className={`transform transition-transform duration-300 ${isCollapsed ? 'rotate-90' : '-rotate-90'}`}
                                />
                            </button>
                        )}
                    </div>

                    {/* User Info */}
                    {!isCollapsed && (
                        <div className="px-6 py-4 border-b border-orange-500/30 flex-shrink-0">
                            <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 rounded-full bg-orange-500 flex items-center justify-center">
                                    <FaUser className="text-white" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-white">{userName}</p>
                                    <p className="text-xs text-orange-200">Master Admin</p>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Navigation */}
                    <nav className="flex-shrink-0 py-6">
                        <ul className="space-y-1.5">
                            {menuItems.map((item) => (
                                <li key={item.path}>
                                    <Link
                                        href={item.path}
                                        className={`
                                            flex items-center ${isCollapsed ? 'justify-center px-3' : 'px-6'} 
                                            py-3 space-x-3
                                            ${pathname === item.path 
                                                ? 'bg-orange-700/60 border-r-4 border-orange-300' 
                                                : 'hover:bg-orange-500/40'
                                            }
                                            transition-all duration-200
                                        `}
                                    >
                                        <item.icon
                                            className={`text-xl ${
                                                pathname === item.path ? 'text-white' : 'text-orange-200'
                                            }`}
                                        />
                                        {!isCollapsed && (
                                            <span className="text-sm font-medium">{item.label}</span>
                                        )}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </nav>

                    {/* Footer */}
                    <div className="p-6 mt-auto border-t border-orange-500/30 space-y-4 flex-shrink-0">
                        {/* Version Info */}
                        <div
                            className="relative cursor-pointer"
                            onClick={() => {
                                setShowChanges(!showChanges);
                                setIsNewVersion(false);
                            }}
                        >
                            <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
                                {!isCollapsed && <span className="text-sm">Versiyon 2.1.0</span>}
                                <FaInfoCircle className="text-orange-200 hover:text-white transition-colors" />
                                {isNewVersion && !isCollapsed && (
                                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
                                        Yeni
                                    </span>
                                )}
                            </div>

                            {showChanges && !isCollapsed && (
                                <div className="mt-4 bg-orange-500/20 rounded-lg p-4">
                                    <p className="text-sm font-medium mb-2">Değişiklikler:</p>
                                    <ul className="space-y-1.5">
                                        {recentUpdates.map((update, index) => (
                                            <li key={index} className="flex items-center space-x-2 text-sm text-orange-100">
                                                <span className="w-1.5 h-1.5 bg-orange-300 rounded-full" />
                                                <span>{update}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>

                        {/* Logout Button */}
                        <button
                            onClick={() => {
                                auth.signOut();
                                router.push('/login');
                            }}
                            className={`
                                group flex items-center ${isCollapsed ? 'justify-center' : 'justify-between'}
                                w-full bg-orange-800 hover:bg-orange-900 text-white p-3 rounded-lg
                                shadow-lg transition-all duration-200 hover:-translate-y-0.5
                            `}
                        >
                            {!isCollapsed && <span className="text-sm font-medium">Çıkış Yap</span>}
                            <FaSignOutAlt className="text-orange-200 group-hover:text-white transition-colors" />
                        </button>

                        {/* Credits */}
                        {!isCollapsed && (
                            <p className="text-center text-orange-200 text-xs">
                                <a
                                    href="https://felixart.com.tr"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:text-white transition-colors"
                                >
                                    Felixart
                                </a> tarafından ♥ ile geliştirilmiştir
                            </p>
                        )}
                    </div>
                </aside>

                {/* Main Content */}
                <main
                    className={`
                        flex-grow 
                        ${isMobile ? 'mt-16 px-4 py-6' : `p-8 ${isCollapsed ? 'ml-20' : 'ml-72'}`}
                        transition-all duration-300
                        bg-gray-50
                    `}
                >
                    <div className="max-w-7xl mx-auto">
                        {children}
                    </div>
                </main>
            </div>
        </body>
        </html>
    );
}