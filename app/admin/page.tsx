// app/admin/page.tsx
'use client';

import {useEffect} from 'react';
import {useRouter} from 'next/navigation';
import {auth, db} from '@/config/firebaseConfig';
import {onAuthStateChanged} from 'firebase/auth';
import {doc, getDoc} from 'firebase/firestore';
import Image from 'next/image';
import Link from 'next/link';

export default function AdminPage() {
    const router = useRouter();

    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, async (user) => {
            if (user) {
                const docRef = doc(db, 'users', user.uid);
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const userData = docSnap.data();

                    if (userData.role !== 'master') {
                        router.push('/unauthorized');
                    }
                } else {
                    router.push('/login');
                }
            } else {
                router.push('/login');
            }
        });

        return () => unsubscribe();
    }, [router]);

    return (
        <div className="flex-grow p-10">
            <div className="bg-white p-6 rounded-lg shadow-md mb-6 flex items-center justify-between">
                <h1 className="text-4xl font-bold text-orange-600">Hoşgeldiniz, Master Admin!</h1>
                <Image src="/app_logo.png" alt="Yemek Kapımda Logo" width={70} height={70}/>
            </div>

            {/* Sağ Tarafta Tüm Menü Öğeleri */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                <Link href="/sliders">
                    <div
                        className="bg-gradient-to-r from-purple-400 to-purple-500 p-6 rounded-lg shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-transform duration-200 text-white">
                        <h2 className="text-2xl font-bold mb-4">Slaytlar</h2>
                        <p>Slaytları buradan yönetebilirsiniz.</p>
                    </div>
                </Link>
                <Link href="/users">
                    <div
                        className="bg-gradient-to-r from-orange-400 to-orange-500 p-6 rounded-lg shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-transform duration-200 text-white">
                        <h2 className="text-2xl font-bold mb-4">Müşteri Yönetimi</h2>
                        <p>Kayıtlı müşterileri buradan yönetebilirsiniz.</p>
                    </div>
                </Link>
                <Link href="/firms">
                    <div
                        className="bg-gradient-to-r from-green-400 to-green-500 p-6 rounded-lg shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-transform duration-200 text-white">
                        <h2 className="text-2xl font-bold mb-4">Firma Yönetimi</h2>
                        <p>Firmaları buradan yönetebilirsiniz.</p>
                    </div>
                </Link>
                <Link href="/couriers">
                    <div
                        className="bg-gradient-to-r from-blue-400 to-blue-500 p-6 rounded-lg shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-transform duration-200 text-white">
                        <h2 className="text-2xl font-bold mb-4">Kuryeler</h2>
                        <p>Kuryeleri buradan yönetebilirsiniz.</p>
                    </div>
                </Link>
                <Link href="/orders">
                    <div
                        className="bg-gradient-to-r from-yellow-400 to-yellow-500 p-6 rounded-lg shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-transform duration-200 text-white">
                        <h2 className="text-2xl font-bold mb-4">Siparişler</h2>
                        <p>Siparişlerinizi buradan yönetebilirsiniz.</p>
                    </div>
                </Link>
                <Link href="/products">
                    <div
                        className="bg-gradient-to-r from-pink-400 to-pink-500 p-6 rounded-lg shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-transform duration-200 text-white">
                        <h2 className="text-2xl font-bold mb-4">Ürünler</h2>
                        <p>Ürünlerinizi buradan yönetebilirsiniz.</p>
                    </div>
                </Link>
                <Link href="/ratings">
                    <div
                        className="bg-gradient-to-r from-red-400 to-red-500 p-6 rounded-lg shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-transform duration-200 text-white">
                        <h2 className="text-2xl font-bold mb-4">Değerlendirmeler</h2>
                        <p>Değerlendirmeleri buradan yönetebilirsiniz.</p>
                    </div>
                </Link>
                <Link href="/statics">
                    <div
                        className="bg-gradient-to-r from-gray-400 to-gray-500 p-6 rounded-lg shadow-lg hover:shadow-2xl transform hover:-translate-y-1 transition-transform duration-200 text-white">
                        <h2 className="text-2xl font-bold mb-4">İstatistikler</h2>
                        <p>İstatistikleri buradan yönetebilirsiniz.</p>
                    </div>
                </Link>
            </div>
        </div>
    );
}