'use client';

import { useState, useEffect } from 'react';
import { db } from '@/config/firebaseConfig';
import { doc, getDoc, setDoc, onSnapshot } from 'firebase/firestore';
import { Ticket, Save, AlertCircle, CheckCircle } from 'lucide-react';

interface FirstOrderCouponSettings {
    code: string;
    minOrderAmount: number;
    discountAmount: number;
    isActive: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

export default function FirstOrderCouponPage() {
    const [settings, setSettings] = useState<FirstOrderCouponSettings>({
        code: '',
        minOrderAmount: 0,
        discountAmount: 0,
        isActive: false
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

    // Real-time güncellemeler için Firestore listener
    useEffect(() => {
        const docRef = doc(db, 'settings', 'firstOrderCoupon');
        
        const unsubscribe = onSnapshot(docRef, (docSnap) => {
            if (docSnap.exists()) {
                const data = docSnap.data() as FirstOrderCouponSettings;
                setSettings(data);
            } else {
                // Varsayılan değerler
                setSettings({
                    code: 'ILKSIPARIS',
                    minOrderAmount: 50,
                    discountAmount: 10,
                    isActive: true
                });
            }
            setLoading(false);
        }, (error) => {
            console.error('Firestore listener error:', error);
            setMessage({ type: 'error', text: 'Veriler yüklenirken hata oluştu.' });
            setLoading(false);
        });

        return () => unsubscribe();
    }, []);

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setSaving(true);
        setMessage(null);

        try {
            const docRef = doc(db, 'settings', 'firstOrderCoupon');
            const updatedSettings = {
                ...settings,
                updatedAt: new Date(),
                createdAt: settings.createdAt || new Date()
            };

            await setDoc(docRef, updatedSettings);
            setMessage({ type: 'success', text: 'Kupon ayarları başarıyla kaydedildi!' });
        } catch (error) {
            console.error('Kaydetme hatası:', error);
            setMessage({ type: 'error', text: 'Kaydetme sırasında hata oluştu. Lütfen tekrar deneyin.' });
        } finally {
            setSaving(false);
        }
    };

    const handleInputChange = (field: keyof FirstOrderCouponSettings, value: string | number | boolean) => {
        setSettings(prev => ({
            ...prev,
            [field]: value
        }));
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Yükleniyor...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="max-w-4xl mx-auto p-6 sm:p-8">
                {/* Header Section */}
                <div className="mb-8">
                    <div className="flex items-center gap-3 mb-2">
                        <Ticket className="w-8 h-8 text-orange-600" />
                        <h1 className="text-2xl font-bold text-gray-800">İlk Sipariş Kupon Ayarları</h1>
                    </div>
                    <p className="text-gray-600">Yeni müşteriler için ilk sipariş kuponunu buradan yönetebilirsiniz.</p>
                </div>

                {/* Message Display */}
                {message && (
                    <div className={`mb-6 flex items-center gap-2 p-4 rounded-lg border ${
                        message.type === 'success' 
                            ? 'bg-green-50 text-green-700 border-green-100' 
                            : 'bg-red-50 text-red-700 border-red-100'
                    }`}>
                        {message.type === 'success' ? (
                            <CheckCircle className="w-5 h-5" />
                        ) : (
                            <AlertCircle className="w-5 h-5" />
                        )}
                        <p>{message.text}</p>
                    </div>
                )}

                {/* Form */}
                <div className="bg-white shadow-xl rounded-xl overflow-hidden">
                    <div className="bg-gradient-to-r from-orange-600 to-red-600 p-6">
                        <h2 className="text-xl font-bold text-white">Kupon Detayları</h2>
                    </div>
                    
                    <form onSubmit={handleSubmit} className="p-8 space-y-6">
                        {/* Kupon Kodu */}
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">
                                Kupon Kodu <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                value={settings.code}
                                onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                                className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Örn: ILKSIPARIS"
                                required
                                maxLength={20}
                            />
                            <p className="text-sm text-gray-500 mt-1">Müşterilerin kullanacağı kupon kodu</p>
                        </div>

                        {/* Minimum Sipariş Tutarı */}
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">
                                Minimum Sipariş Tutarı (TL) <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="number"
                                value={settings.minOrderAmount}
                                onChange={(e) => handleInputChange('minOrderAmount', Number(e.target.value))}
                                className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                placeholder="50"
                                required
                                min="0"
                                step="0.01"
                            />
                            <p className="text-sm text-gray-500 mt-1">Kuponun geçerli olması için gereken minimum sipariş tutarı</p>
                        </div>

                        {/* İndirim Tutarı */}
                        <div>
                            <label className="block text-gray-700 font-medium mb-2">
                                İndirim Tutarı (TL) <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="number"
                                value={settings.discountAmount}
                                onChange={(e) => handleInputChange('discountAmount', Number(e.target.value))}
                                className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                placeholder="10"
                                required
                                min="0"
                                step="0.01"
                            />
                            <p className="text-sm text-gray-500 mt-1">Müşteriye sağlanacak indirim tutarı</p>
                        </div>

                        {/* Aktif/Pasif Durumu */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <label className="block text-gray-700 font-medium mb-1">
                                        Kupon Durumu
                                    </label>
                                    <p className="text-sm text-gray-500">
                                        Kuponun aktif olup olmadığını belirler
                                    </p>
                                </div>
                                <label className="relative inline-flex items-center cursor-pointer">
                                    <input
                                        type="checkbox"
                                        checked={settings.isActive}
                                        onChange={(e) => handleInputChange('isActive', e.target.checked)}
                                        className="sr-only peer"
                                    />
                                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                                    <span className="ml-3 text-sm font-medium text-gray-700">
                                        {settings.isActive ? 'Aktif' : 'Pasif'}
                                    </span>
                                </label>
                            </div>
                        </div>

                        {/* Kaydet Butonu */}
                        <div className="pt-6">
                            <button
                                type="submit"
                                disabled={saving}
                                className="w-full bg-orange-600 text-white px-6 py-3 rounded-lg shadow-lg hover:bg-orange-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                            >
                                {saving ? (
                                    <>
                                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                                        Kaydediliyor...
                                    </>
                                ) : (
                                    <>
                                        <Save className="w-5 h-5" />
                                        Ayarları Kaydet
                                    </>
                                )}
                            </button>
                        </div>
                    </form>
                </div>

                {/* Bilgi Kutusu */}
                <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-blue-800 mb-3">Kupon Nasıl Çalışır?</h3>
                    <ul className="text-blue-700 space-y-2">
                        <li>• Bu kupon sadece ilk sipariş veren müşteriler tarafından kullanılabilir</li>
                        <li>• Minimum sipariş tutarına ulaşan siparişlerde geçerlidir</li>
                        <li>• İndirim tutarı sabit bir miktar olarak uygulanır</li>
                        <li>• Kupon pasif durumda iken müşteriler tarafından kullanılamaz</li>
                    </ul>
                </div>
            </div>
        </div>
    );
}
