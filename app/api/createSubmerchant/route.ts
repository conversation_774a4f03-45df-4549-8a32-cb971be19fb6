import { NextResponse } from 'next/server';
import axios from 'axios';
import { getAuth } from 'firebase-admin/auth';
import { initializeApp, cert, ServiceAccount } from 'firebase-admin/app';
import { v4 as uuidv4 } from 'uuid';
import serviceAccount from '@/config/yemekkapimda-46ecc-firebase-adminsdk-2hyq9-91da2100ca.json';

// Firebase Admin SDK initialization
const app = initializeApp({
    credential: cert(serviceAccount as ServiceAccount)
});

export async function POST(req: Request) {
    const body = await req.json();

    const { name, email, gsmNumber, address, iban, identityNumber, subMerchantExternalId, subMerchantType } = body;

    const idToken = req.headers.get('authorization')?.split(' ')[1];
    if (!idToken) {
        return NextResponse.json({ message: 'Yet<PERSON><PERSON> erişim' }, { status: 401 });
    }

    try {
        const decodedToken = await getAuth(app).verifyIdToken(idToken);
        console.log('Doğrulanan kullanıcı UID:', decodedToken.uid);

        // Benzersiz bir ID oluşturuyoruz
        const conversationId = uuidv4();

        // Iyzico API'sine POST isteği yapıyoruz
        const response = await axios.post(
            `${process.env.NEXT_PUBLIC_IYZICO_API_URL}/onboarding/submerchant`,
            {
                locale: "tr",
                conversationId,
                name,
                email,
                gsmNumber,
                address,
                iban,
                identityNumber,
                subMerchantExternalId,
                subMerchantType,
                currency: "TRY",
            },
            {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${process.env.NEXT_PUBLIC_IYZICO_API_KEY}:${process.env.NEXT_PUBLIC_IYZICO_SECRET_KEY}`).toString('base64')}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        // API'den gelen yanıtı döndürüyoruz
        return NextResponse.json(response.data, { status: 200 });
    } catch (error: any) {
        console.error('Iyzico API hatası:', error);
        return NextResponse.json({ message: 'Bir hata oluştu', error: error.response?.data }, { status: 500 });
    }
}