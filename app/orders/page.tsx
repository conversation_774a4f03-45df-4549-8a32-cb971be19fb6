'use client';

import React, { useState, useEffect, useRef } from 'react';
import { doc, updateDoc, getDoc, collection, onSnapshot, getDocs, query, where } from 'firebase/firestore';
import { onAuthStateChanged } from 'firebase/auth';
import { db, auth } from '@/config/firebaseConfig';
import { useFirestoreCollection } from '@/hooks/useFirestoreCollection';
import OrderItem from '@/components/OrderItem';
import OrderTableHeader from '@/components/OrderHeader';
import Modal from '@/components/Modal';
import SoundToggle from '@/components/SoundToggle';
import { Order, Courier } from '@/types/Order';

export default function OrdersPage() {
    const { loading } = useFirestoreCollection<Order>('orders');
    const [orders, setOrders] = useState<Order[]>([]);
    const [couriers, setCouriers] = useState<Courier[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalData] = useState<{ orderId?: string, type?: string }>({});
    const [soundEnabled, setSoundEnabled] = useState(true);
    const [, setFirmId] = useState<string | null>(null);
    const [, setUserId] = useState<string | null>(null);
    const audioRef = useRef<HTMLAudioElement | null>(null);
    const previousOrdersCount = useRef<number>(0);
    const initialLoadRef = useRef(true);

    useEffect(() => {
        fetchCouriers();
        audioRef.current = new Audio('/bildirim.mp3');

        const ordersCollectionRef = collection(db, 'orders');
        const unsubscribe = onSnapshot(ordersCollectionRef, (querySnapshot) => {
            const newOrders = querySnapshot.docs.map(doc => ({ ...doc.data(), id: doc.id })) as Order[];
            newOrders.sort((a, b) => b.orderDate.toDate() - a.orderDate.toDate());

            if (!initialLoadRef.current && querySnapshot.size > previousOrdersCount.current && soundEnabled) {
                console.log('Yeni sipariş geldi!');
                audioRef.current?.play().catch(err => console.error("Ses çalma hatası:", err));
            }

            setOrders(newOrders);
            previousOrdersCount.current = querySnapshot.size;
            initialLoadRef.current = false;
        });

        return () => {
            unsubscribe();
            if (audioRef.current) {
                audioRef.current.pause();
                audioRef.current = null;
            }
        };
    }, [soundEnabled]);

    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, async (user) => {
            if (user) {
                setUserId(user.uid);
                const q = query(collection(db, 'firms'), where('userId', '==', user.uid));
                const querySnapshot = await getDocs(q);
                if (!querySnapshot.empty) {
                    setFirmId(querySnapshot.docs[0].id);
                } else {
                    console.error("Firma bulunamadı.");
                }
            } else {
                console.error("Kullanıcı oturumu açmamış.");
            }
        });

        return () => unsubscribe();
    }, []);

    const fetchCouriers = async () => {
        const courierList = await getDocs(collection(db, 'couriers'));
        setCouriers(courierList.docs.map(doc => ({
            id: doc.id,
            name: doc.data().name
        })));
    };

    const handleStatusUpdate = async (orderId: string, actionType: string) => {
        const orderDocRef = doc(db, 'orders', orderId);
        const orderSnap = await getDoc(orderDocRef);

        if (!orderSnap.exists()) {
            console.error("Sipariş bulunamadı");
            return;
        }

        const orderData = orderSnap.data() as Order;
        const userId = orderData.userId;
        const firmIds = Array.from(new Set(orderData.items.map(item => item.firmId)));

        const paths = [
            `orders/${orderId}`,
            ...firmIds.map(firmId => `firms/${firmId}/orders/${orderId}`),
            `users/${userId}/orders/${orderId}`
        ];

        try {
            await Promise.all(
                paths.map(path => updateDoc(doc(db, path), { status: actionType }))
            );
        } catch (error) {
            console.error("Error updating order status:", error);
        }

        setIsModalOpen(false);
    };

    const handleCourierAssignment = async (orderId: string, courierId: string) => {
        const orderDocRef = doc(db, 'orders', orderId);
        const orderSnap = await getDoc(orderDocRef);

        if (!orderSnap.exists()) {
            console.error("Sipariş bulunamadı");
            return;
        }

        const orderData = orderSnap.data() as Order;
        const userId = orderData.userId;
        const firmIds = Array.from(new Set(orderData.items.map(item => item.firmId)));

        const paths = [
            `orders/${orderId}`,
            ...firmIds.map(firmId => `firms/${firmId}/orders/${orderId}`),
            `users/${userId}/orders/${orderId}`
        ];

        try {
            await Promise.all(
                paths.map(path => updateDoc(doc(db, path), { assignedCourier: courierId }))
            );
        } catch (error) {
            console.error("Error assigning courier:", error);
        }
    };

    const calculateTotalPrice = (items: { price: number, quantity: number }[]) => {
        return items.reduce((total, item) => total + item.price * item.quantity, 0);
    };

    if (loading) return <div>Loading...</div>;

    return (
        <div className="p-8 bg-gray-100 min-h-screen">
            <h1 className="text-4xl font-extrabold mb-8 text-center text-gray-700">
                Tüm Siparişler
            </h1>

            <SoundToggle
                soundEnabled={soundEnabled}
                onToggle={() => setSoundEnabled(!soundEnabled)}
            />

            <br/>

            <table className="min-w-full bg-white shadow-md rounded-lg border border-gray-200">
                <OrderTableHeader/>
                <tbody>
                {orders.map((order, index) => (
                    <OrderItem
                        key={order.id}
                        order={order}
                        couriers={couriers}
                        onStatusUpdate={handleStatusUpdate}
                        onCourierAssignment={handleCourierAssignment}
                        calculateTotalPrice={calculateTotalPrice}
                        index={orders.length - index}
                    />
                ))}
                </tbody>
            </table>

            {isModalOpen && (
                <Modal
                    isOpen={true}
                    title="Sipariş İşlemi"
                    message="Sipariş durumu güncelleniyor."
                    onConfirm={() => handleStatusUpdate(modalData.orderId!, modalData.type!)}
                    onClose={() => setIsModalOpen(false)}
                    confirmLabel="Onayla"
                    cancelLabel="İptal"
                />
            )}
        </div>
    );
}