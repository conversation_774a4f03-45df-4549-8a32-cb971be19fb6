import { useEffect, useState } from 'react';
import { collection, getDocs, QueryDocumentSnapshot } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';

export const useFirestoreCollection = <T,>(collectionName: string) => {
    const [data, setData] = useState<T[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<Error | null>(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const querySnapshot = await getDocs(collection(db, collectionName));
                const dataList = querySnapshot.docs.map((doc: QueryDocumentSnapshot) => ({ id: doc.id, ...doc.data() } as T));
                setData(dataList);
            } catch (err) {
                setError(err as Error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [collectionName]);

    return { data, loading, error, setData };
};

export default useFirestoreCollection;