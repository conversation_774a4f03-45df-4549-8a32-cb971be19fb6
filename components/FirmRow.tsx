import React, { useState } from 'react';
import Link from 'next/link';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '@/config/firebaseConfig';
import Button from './Button';
import Switch from 'react-switch';
import Image from 'next/image';

interface Firm {
    id: string;
    name: string;
    description: string;
    banner: string;
    visible: boolean;
}

interface FirmRowProps {
    firm: Firm;
    onDelete: (id: string) => void;
}

const FirmRow: React.FC<FirmRowProps> = ({ firm, onDelete }) => {
    const [isVisible, setIsVisible] = useState(firm.visible);

    const toggleVisibility = async () => {
        try {
            const firmDocRef = doc(db, 'firms', firm.id);
            await updateDoc(firmDocRef, { visible: !isVisible });
            setIsVisible(!isVisible);
            console.log(`Visibility toggled for firm ${firm.id} to ${!isVisible}`);
        } catch (error) {
            console.error("Error toggling visibility:", error);
        }
    };

    return (
        <tr className="hover:bg-gray-100">
            <td className="py-2 px-4 border border-gray-300">
                <div className="relative w-10 h-10">
                    <Image
                        src={firm.banner}
                        alt={`${firm.name} banner`}
                        fill
                        className="object-cover rounded-lg"
                        sizes="40px"
                    />
                </div>
            </td>
            <td className="py-2 px-4 border border-gray-300">{firm.name}</td>
            <td className="py-2 px-4 border border-gray-300">{firm.description}</td>
            <td className="py-2 px-4 border border-gray-300">
                <div className="flex space-x-2 items-center">
                    <Link href={`/firms/${firm.id}`}>
                        <Button className="bg-blue-500 hover:bg-blue-600 text-white text-sm">Güncelle</Button>
                    </Link>
                    <Button onClick={() => onDelete(firm.id)} className="bg-red-500 hover:bg-red-600 text-white text-sm">Sil</Button>
                </div>
            </td>
            <td className="py-2 px-4 border border-gray-300">
                <Switch
                    onChange={toggleVisibility}
                    checked={isVisible}
                    onColor="#00C853"
                    offColor="#D32F2F"
                    uncheckedIcon={false}
                    checkedIcon={false}
                />
            </td>
        </tr>
    );
};

export default FirmRow;