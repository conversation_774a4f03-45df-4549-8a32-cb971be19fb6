import { useState, useEffect } from 'react';
import { Order, Courier, Addresses } from '@/types/Order';
import Image from 'next/image';

interface Props {
    order: Order;
    couriers: Courier[];
    onStatusUpdate: (orderId: string, actionType: string) => void;
    onCourierAssignment: (orderId: string, courierId: string) => void;
    calculateTotalPrice: (items: { price: number, quantity: number }[]) => number;
    index: number;
}

export default function OrderItem({
                                      order,
                                      couriers,
                                      onStatusUpdate,
                                      onCourierAssignment,
                                      calculateTotalPrice,
                                      index
                                  }: Props) {
    const [isExpanded, setIsExpanded] = useState(false);
    const [assignedCourierName, setAssignedCourierName] = useState('');
    const [deliveryAddress, setDeliveryAddress] = useState<Addresses | null>(null);
    const [selectedCourier, setSelectedCourier] = useState<string>('');

    const totalPrice = calculateTotalPrice(order.items);
    const hasPriceDiscount = order.finalPrice && order.finalPrice !== totalPrice;
    const isNewOrder = order.status === 'Sipariş Alındı';

    // Kurye bilgisini güncelle
    useEffect(() => {
        const courier = couriers.find(c => c.id === order.assignedCourier);
        setAssignedCourierName(courier?.name || '');
    }, [order.assignedCourier, couriers]);

    // Teslimat adresini bul
    useEffect(() => {
        if (order.deliverTo) {
            const address = order.addresses?.find(addr => addr.id === order.deliverTo);
            setDeliveryAddress(address || null);
        }
    }, [order.addresses, order.deliverTo]);

    // Sipariş durumunu kontrol et
    useEffect(() => {
        const allItemsReady = order.items.every(item => item.status === 'paket hazır');
        if (allItemsReady &&
            order.status !== 'Paketler Hazır' &&
            order.status !== 'Teslim Edildi') {
            onStatusUpdate(order.id, 'Paketler Hazır');
        }
    }, [order.items, order.status, onStatusUpdate, order.id]);

    // Kurye atama işlemi
    const handleCourierAssignment = () => {
        if (!selectedCourier) {
            alert('Lütfen bir kurye seçin');
            return;
        }
        onCourierAssignment(order.id, selectedCourier);
        setSelectedCourier('');
    };

    // Firma sayılarını hesapla
    const firmCounts = order.items.reduce((acc, item) => {
        acc[item.firmName] = (acc[item.firmName] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    return (
        <>
            {/* Ana Sipariş Satırı */}
            <tr
                onClick={() => setIsExpanded(!isExpanded)}
                className={`cursor-pointer transition-all duration-200 ${
                    isExpanded ? 'bg-blue-50' : 'hover:bg-gray-50'
                } relative`}
            >
                <td className="py-4 px-6 border-b text-left font-medium">{index}</td>
                <td className="py-4 px-6 border-b text-left">
                    <div className="text-sm text-gray-600">
                        {order.orderDate.toDate().toLocaleDateString('tr-TR')}
                    </div>
                    <div className="text-xs text-gray-500">
                        {order.orderDate.toDate().toLocaleTimeString('tr-TR')}
                    </div>
                </td>
                <td className="py-4 px-6 border-b text-left">
                    <div className="text-sm font-medium">{order.id}</div>
                </td>
                <td className="py-4 px-6 border-b text-left">
                    <div className="flex flex-col">
                        <span className="font-medium">{order.firstName} {order.lastName}</span>
                        <span className="text-xs text-gray-500">{order.phoneNumber}</span>
                    </div>
                </td>
                <td className="py-4 px-6 border-b text-left">
                    {hasPriceDiscount ? (
                        <div>
                            <div className="text-gray-500 line-through text-sm">
                                {totalPrice.toFixed(2)} TL
                            </div>
                            <div className="text-green-600 font-semibold">
                                {order.finalPrice.toFixed(2)} TL
                            </div>
                            <div className="text-xs text-red-500">
                                -{((totalPrice - order.finalPrice) / totalPrice * 100).toFixed(0)}% indirim
                            </div>
                        </div>
                    ) : (
                        <div className="font-medium">{totalPrice.toFixed(2)} TL</div>
                    )}
                </td>
                <td className="py-4 px-6 border-b text-left">
                    <div className="flex items-center gap-2">
                        <span className={`px-3 py-1 rounded-full text-sm ${
                            order.status === 'Sipariş Alındı' ? 'bg-yellow-100 text-yellow-800' :
                            order.status === 'Siparişler Hazırlanıyor' ? 'bg-blue-100 text-blue-800' :
                            order.status === 'Paketler Hazır' ? 'bg-indigo-100 text-indigo-800' :
                            order.status === 'Yola Çıktı' ? 'bg-purple-100 text-purple-800' :
                            order.status === 'Teslim Edildi' ? 'bg-green-100 text-green-800' :
                            order.status === 'İptal' ? 'bg-red-100 text-red-800' :
                            order.status === 'İade' ? 'bg-orange-100 text-orange-800' :
                            'bg-gray-100 text-gray-800'
                        }`}>
                            {order.status}
                        </span>
                        {isNewOrder && (
                            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full animate-pulse">
                                Yeni
                            </span>
                        )}
                    </div>
                </td>
                <td className="py-4 px-6 border-b text-left">
                    {assignedCourierName ? (
                        <div className="flex items-center gap-2">
                            <span className="w-2 h-2 rounded-full bg-green-500"></span>
                            <span>{assignedCourierName}</span>
                        </div>
                    ) : (
                        <span className="text-gray-400">Atanmadı</span>
                    )}
                </td>
            </tr>

            {/* Firma Bilgileri */}
            <tr>
                <td colSpan={7} className="py-2 px-6 bg-gray-50">
                    <div className="flex gap-2 flex-wrap">
                        {Object.entries(firmCounts).map(([firm, count], index) => (
                            <div key={index} 
                                 className="text-sm bg-orange-100 text-orange-800 px-3 py-1 rounded-full">
                                {firm} ({count})
                            </div>
                        ))}
                    </div>
                </td>
            </tr>

            {/* Detaylı Sipariş Bilgileri */}
            {isExpanded && (
                <tr>
                    <td colSpan={7} className="p-5 bg-gray-50 border-b">
                        <div className="bg-white p-4 shadow-inner rounded-lg">
                            {/* Temel Bilgiler */}
                            <div className="space-y-3 mb-4">
                                {/* Yeni Sipariş Bildirimi */}
                                {isNewOrder && (
                                    <div className="bg-yellow-50 p-3 rounded-md border border-yellow-200">
                                        <p className="text-yellow-700">
                                            <span className="font-semibold">⚡ Yeni Sipariş!</span>
                                            <br />
                                            Lütfen en kısa sürede işleme alın.
                                        </p>
                                    </div>
                                )}

                                {/* Fiyat Bilgisi */}
                                {hasPriceDiscount && (
                                    <div className="bg-green-50 p-3 rounded-md border border-green-200">
                                        <p className="text-green-700">
                                            <span className="font-semibold">💰 İndirimli Sipariş</span>
                                            <br />
                                            Orijinal Tutar: <span className="line-through">{totalPrice.toFixed(2)} TL</span>
                                            <br />
                                            İndirimli Tutar: <span className="font-bold">{order.finalPrice.toFixed(2)} TL</span>
                                            <br />
                                            Toplam İndirim: <span className="font-bold text-red-600">
                                                -{(totalPrice - order.finalPrice).toFixed(2)} TL
                                            </span>
                                        </p>
                                    </div>
                                )}

                                <p className="text-gray-700">
                                    <span className="font-semibold">Ödeme Yöntemi:</span> {order.paymentMethod}
                                </p>
                                <p className="text-gray-700">
                                    <span className="font-semibold">Sipariş Veren:</span>{' '}
                                    {order.firstName} {order.lastName}, {order.phoneNumber}
                                </p>
                                {deliveryAddress && (
                                    <p className="text-gray-700">
                                        <span className="font-semibold">Teslimat Adresi:</span>{' '}
                                        {deliveryAddress.sokak}, {deliveryAddress.mahalle},{' '}
                                        {deliveryAddress.bina_ve_daire}
                                        {deliveryAddress.aciklama && ` (${deliveryAddress.aciklama})`}
                                    </p>
                                )}
                                <div className="space-y-2 mt-2">
                                    {(order.instructions || order.deliveryInstructions) && (
                                        <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
                                            <p className="font-semibold text-blue-700 mb-2">📝 Teslimat Notları:</p>
                                            
                                            {order.instructions && (
                                                <p className="text-gray-700 mb-2">
                                                    <span className="font-semibold">Genel Not:</span>{' '}
                                                    {order.instructions}
                                                </p>
                                            )}
                                            
                                            {order.deliveryInstructions && (
                                                <div className="space-y-1">
                                                    {Array.isArray(order.deliveryInstructions) 
                                                        ? order.deliveryInstructions.map((instruction, index) => (
                                                            <p key={index} className="text-gray-700">
                                                                <span className="font-semibold">Not {index + 1}:</span>{' '}
                                                                {instruction}
                                                            </p>
                                                        ))
                                                        : Object.values(order.deliveryInstructions as Record<string, string>).map((instruction, index) => (
                                                            <p key={index} className="text-gray-700">
                                                                <span className="font-semibold">Not {index + 1}:</span>{' '}
                                                                {instruction}
                                                            </p>
                                                        ))
                                                    }
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Ürün Listesi */}
                            <div className="mt-6">
                                <h3 className="text-lg font-semibold mb-3">Ürünler</h3>
                                <ul className="space-y-4">
                                    {order.items.map((item, index) => (
                                        <li key={index} className="flex justify-between items-center border-b pb-4">
                                            <div className="flex items-center space-x-4">
                                                <Image
                                                    src={item.imageURL}
                                                    alt={item.productName}
                                                    width={50}
                                                    height={50}
                                                    className="rounded-lg object-cover"
                                                />
                                                <div>
                                                    <p className="font-semibold">{item.productName}</p>
                                                    <p className="text-sm text-gray-600">
                                                        {item.price.toFixed(2)} TL x {item.quantity} = {(item.price * item.quantity).toFixed(2)} TL
                                                    </p>
                                                    <p className="text-sm text-gray-500">Firma: {item.firmName}</p>
                                                    {/* Seçilen Özellikler */}
                                                    {item.selectedFeatures && item.selectedFeatures.length > 0 && (
                                                        <div className="mt-1">
                                                            <p className="text-xs text-gray-500 font-medium">Seçilen Özellikler:</p>
                                                            {item.selectedFeatures.map((feature, idx) => (
                                                                <span key={idx} className="text-xs text-gray-600 mr-2">
                                                                    {feature.name}: {feature.price} TL
                                                                </span>
                                                            ))}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex flex-col items-end space-y-2">
                                                <span className="text-gray-600">{item.status}</span>
                                            </div>
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            {/* Kurye ve Durum Kontrolleri */}
                            <div className="mt-6 flex items-center gap-4">
                                <div className="flex items-center gap-2">
                                    <select
                                        value={selectedCourier}
                                        onChange={e => setSelectedCourier(e.target.value)}
                                        className="p-2 border rounded-md"
                                    >
                                        <option value="">Kurye Seçin</option>
                                        {couriers.map(courier => (
                                            <option key={courier.id} value={courier.id}>
                                                {courier.name}
                                            </option>
                                        ))}
                                    </select>
                                    <button
                                        onClick={handleCourierAssignment}
                                        disabled={!selectedCourier}
                                        className={`px-4 py-2 rounded-md ${
                                            selectedCourier
                                                ? 'bg-purple-500 hover:bg-purple-600'
                                                : 'bg-gray-400 cursor-not-allowed'
                                        } text-white transition-colors`}
                                    >
                                        Kurye Ata
                                    </button>
                                </div>

                                <select
                                    onChange={(e) => onStatusUpdate(order.id, e.target.value)}
                                    className="p-2 border rounded-md"
                                    value={order.status}
                                >
                                    <option value="">Durumu Güncelle</option>
                                    <option value="Sipariş Alındı">Sipariş Alındı</option>
                                    <option value="Siparişler Hazırlanıyor">Siparişler Hazırlanıyor</option>
                                    <option value="Paketler Hazır">Paketler Hazır</option>
                                    <option value="Yola Çıktı">Yola Çıktı</option>
                                    <option value="Teslim Edildi">Teslim Edildi</option>
                                    <option value="İptal">İptal</option>
                                    <option value="İade">İade</option>
                                </select>
                            </div>
                        </div>
                    </td>
                </tr>
            )}

            {/* Ayırıcı */}
            <tr>
                <td colSpan={7} className="py-2 bg-gray-200"></td>
            </tr>
        </>
    );
}