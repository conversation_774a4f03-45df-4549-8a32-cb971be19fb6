import React from 'react';

interface DeleteModalProps {
    onClose: () => void;
    onDelete: () => void;
}

const DeleteModal: React.FC<DeleteModalProps> = ({ onClose, onDelete }) => {
    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white p-6 rounded-lg shadow-lg">
                <h2 className="text-xl font-bold mb-4"><PERSON><PERSON><PERSON> istediğinize emin misiniz?</h2>
                <div className="flex justify-end">
                    <button onClick={onClose} className="bg-gray-500 text-white px-4 py-2 rounded-lg mr-2">İptal</button>
                    <button onClick={onDelete} className="bg-red-500 text-white px-4 py-2 rounded-lg">Sil</button>
                </div>
            </div>
        </div>
    );
};

export default DeleteModal;