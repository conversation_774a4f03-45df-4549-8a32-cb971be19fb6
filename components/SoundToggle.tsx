// components/SoundToggle.tsx

interface Props {
    soundEnabled: boolean;
    onToggle: () => void;
}

export default function SoundToggle({ soundEnabled, onToggle }: Props) {
    return (
        <button
            onClick={onToggle}
            className={`px-4 py-2 rounded-md transition duration-200 shadow-md ${soundEnabled ? 'bg-green-500 hover:bg-green-600' : 'bg-red-500 hover:bg-red-600'} text-white`}
        >
            {soundEnabled ? 'Sesi Kapat' : 'Sesi Aç'}
        </button>

    );
}
