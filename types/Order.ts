export interface Order {
    assignedCourier: string;
    id: string;
    userId: string;
    deliverTo: string;
    deliveryInstructions: string[];
    firstName: string;
    lastName: string;
    orderDate: any;
    paymentMethod: string;
    phoneNumber: string;
    instructions: string;
    status: string;
    originalPrice: number;
    finalPrice: number;
    items: OrderItem[];
    addresses: Addresses[];
}

export interface OrderItem {
    addedAt: any;
    categoryId: string;
    categoryName: string;
    firmId: string;
    firmName: string;
    id: string;
    imageURL: string;
    price: number;
    productId: string;
    productName: string;
    quantity: number;
    selectedFeatures: SelectedFeature[];
    status: string;
}

export interface SelectedFeature {
    name: string;
    price: number;
}

export interface Addresses {
    id: string;
    aciklama: string;
    bina_ve_daire: string;
    mahalle: string;
    sokak: string;
}

export interface Courier {
    id: string;
    name: string;
}