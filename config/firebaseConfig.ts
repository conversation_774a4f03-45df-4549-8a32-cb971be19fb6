import { initializeApp, getApp, getApps } from "firebase/app";
import { getAuth } from "firebase/auth";
import { initializeFirestore, CACHE_SIZE_UNLIMITED } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Firebase yapılandırması
const firebaseConfig = {
    apiKey: "AIzaSyDPkqccLCgI9uwbLod0vQjR99hmIn2aKWI",
    authDomain: "yemekkapimda-46ecc.firebaseapp.com",
    projectId: "yemekkapimda-46ecc",
    storageBucket: "yemekkapimda-46ecc.appspot.com",
    messagingSenderId: "201585951998",
    appId: "1:201585951998:web:ddd2de9927fb628b1211af",
    measurementId: "G-H8SR5F9XGF"
};

// Firebase'i bir kere başlat
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();

// Firestore'u gelişmiş ayarlarla başlat
const db = initializeFirestore(app, {
    experimentalAutoDetectLongPolling: true,
    cacheSizeBytes: CACHE_SIZE_UNLIMITED,
    experimentalForceLongPolling: true,
});

// Firebase servislerini başlat
const auth = getAuth(app);
const storage = getStorage(app);

// Analytics'i sadece browser ortamında yükle
let analytics: any = null;
if (typeof window !== "undefined") {
    // Performance izlemeyi devre dışı bırak
    if (window.performance) {
        const originalMark = window.performance.mark;
        const originalMeasure = window.performance.measure;

        window.performance.mark = function(name: string): PerformanceMark {
            try {
                return originalMark.call(window.performance, name);
            } catch {
                return {} as PerformanceMark;
            }
        };

        window.performance.measure = function(
            name: string,
            startMark?: string,
            endMark?: string
        ): PerformanceMeasure {
            try {
                return originalMeasure.call(window.performance, name, startMark, endMark);
            } catch {
                return {} as PerformanceMeasure;
            }
        };
    }
    
    // Analytics'i lazy load et
    import("firebase/analytics").then(({ getAnalytics }) => {
        analytics = getAnalytics(app);
    }).catch((error) => {
        console.error("Analytics yüklenirken hata:", error);
    });
}

// Geliştirme modunda console mesajlarını filtrele
if (process.env.NODE_ENV === 'development') {
    const originalConsoleError = console.error;
    console.error = (...args) => {
        if (typeof args[0] === 'string' && !args[0].includes('heartbeat')) {
            originalConsoleError.apply(console, args);
        }
    };
}

export { app, analytics, auth, db, storage }; 