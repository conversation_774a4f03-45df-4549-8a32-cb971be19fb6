// Firebase SDK'dan gerekli fonksiyonları içe aktarın
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { initializeFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Firebase web uygulamanızın yapılandırması
const firebaseConfig = {
    apiKey: "AIzaSyDPkqccLCgI9uwbLod0vQjR99hmIn2aKWI",
    authDomain: "yemekkapimda-46ecc.firebaseapp.com",
    projectId: "yemekkapimda-46ecc",
    storageBucket: "yemekkapimda-46ecc.appspot.com",
    messagingSenderId: "201585951998",
    appId: "1:201585951998:web:ddd2de9927fb628b1211af",
    measurementId: "G-H8SR5F9XGF"
};

// Firebase'i başlatın
const app = initializeApp(firebaseConfig);

// Firestore'u long polling zorunlu kılacak şekilde başlatın
const db = initializeFirestore(app, {
    experimentalAutoDetectLongPolling: true,
});

// Firebase servislerini başlatın
const auth = getAuth(app);
const storage = getStorage(app);

let analytics;
if (typeof window !== "undefined") {
    import("firebase/analytics").then(({ getAnalytics }) => {
        analytics = getAnalytics(app);
    });
}

// Firebase servislerini dışa aktarın
export { app, analytics, auth, db, storage };
